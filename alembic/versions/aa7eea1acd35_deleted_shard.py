"""deleted-shard

Revision ID: aa7eea1acd35
Revises: ea16f6717343
Create Date: 2025-09-16 11:56:52.439899

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "aa7eea1acd35"
down_revision = "ea16f6717343"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "shard",
        sa.Column("deletion_timestamp", sa.DateTime(), nullable=True),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("shard", "deletion_timestamp", schema="WebApp")
