"""indexes-shard-query

Revision ID: bc129df86156
Revises: aa7eea1acd35
Create Date: 2025-09-16 12:25:29.959751

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "bc129df86156"
down_revision = "aa7eea1acd35"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        "ix_profile_active_by_studio_ver_perm",
        "profile",
        ["studio_id", "active_version_id", "permission_set_uuid"],
        unique=False,
        schema="WebApp",
        mssql_where=sa.text("deletion_timestamp IS NULL"),
    )
    op.create_index(
        "ix_shard_perm_ver_cover_active",
        "shard",
        ["permission_set_uuid", "version"],
        unique=False,
        schema="WebApp",
        mssql_where=sa.text("deletion_timestamp IS NULL"),
        mssql_include=[
            "dataset_id",
            "dataset_name",
            "workspace_id",
            "workspace_name",
            "capacity_id",
            "creation_timestamp",
            "last_refresh_timestamp",
        ],
    )


def downgrade():
    op.drop_index(
        "ix_shard_perm_ver_cover_active",
        table_name="shard",
        schema="WebApp",
        mssql_where=sa.text("deletion_timestamp IS NULL"),
        mssql_include=[
            "dataset_id",
            "dataset_name",
            "workspace_id",
            "workspace_name",
            "capacity_id",
            "creation_timestamp",
            "last_refresh_timestamp",
        ],
    )
    op.drop_index(
        "ix_profile_active_by_studio_ver_perm",
        table_name="profile",
        schema="WebApp",
        mssql_where=sa.text("deletion_timestamp IS NULL"),
    )
