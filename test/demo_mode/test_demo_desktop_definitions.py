def test_generate_demo_mode_token(authorized_client):
    response = authorized_client.get("/demo-mode/desktop-definitions")

    assert response.status_code == 200, response.text
    result = response.json()
    assert "defaults" in result
    assert "measure_groups" in result
    assert "menu_items" in result
    parent_urls = [
        item["dashboard_url"]
        for item in result["menu_items"]
        if "dashboard_url" in item
    ]
    assert all(parent_urls)

    child_urls = [
        child["dashboard_url"]
        for parent in result["menu_items"]
        if "children" in parent
        for child in parent["children"]
        if "dashboard_url" in child
    ]
    assert all(child_urls)
