import datetime

from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from dataset_manager.repo.profile import _DBProfile


def test_delete_profile_nonexistent_studio_returns_404(
    db_session: Session,
    client,
    authorization_header,
):
    response = client.delete(
        "/studio/999999",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_delete_profile(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    responses_mock,
):
    profile_factory(studio_id=42, profile_id="test-profile-id-123")
    responses_mock.delete(
        "https://api.powerbi.com/v1.0/myorg/profiles/test-profile-id-123"
    )
    response = client.delete(
        "/studio/42",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK

    response_data = response.json()
    assert response_data["studio_id"] == 42
    assert response_data["profile_id"] == "test-profile-id-123"
    assert response_data["deletion_timestamp"] is not None

    db_profile = db_session.query(_DBProfile).filter_by(studio_id=42).first()
    assert db_profile is not None
    assert db_profile.deletion_timestamp is not None


def test_delete_profile_already_deleted_studio_returns_404(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
):
    profile_factory(
        studio_id=400,
        profile_id="deleted-profile",
        deletion_timestamp=datetime.datetime.now(datetime.UTC),
    )
    response = client.delete(
        "/studio/400",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}
