from unittest.mock import MagicMock

import pytest
from sqlalchemy.orm import Session

from dataset_manager.api.dependencies import get_capacities_api
from dataset_manager.azure.management.capacities import (
    ManagementCapacitiesAPI,
    get_management_capacities_api,
)
from dataset_manager.azure.powerbi.capacities import CapacitiesAPI
from dataset_manager.azure.powerbi.datasets import DatasetAPI
from dataset_manager.azure.powerbi.profiles import ProfileAPI
from dataset_manager.azure.powerbi.workspaces import WorkspaceAP<PERSON>
from dataset_manager.entities import (
    Environment,
    PermissionSetUUID,
    ProcessedAdlsCredentials,
    ProcessedAdlsURL,
    ServicePrincipalId,
    ShardVersion,
    StudioId,
    VersionId,
)
from dataset_manager.logic.release import (
    assign_releases,
    handle_requested_assign,
    handle_requested_shard,
)
from dataset_manager.logic.shard import (
    AssignToProfileRequest,
    CreatePermissionSetShardRequest,
)
from dataset_manager.repo import Repo
from dataset_manager.repo.capacity import CapacityState, _DBCapacity
from dataset_manager.repo.release import Release, ReleaseStatus, _DBRelease


@pytest.fixture
def capacities_api() -> CapacitiesAPI:
    def dummy_auth_token_provider():
        return "Bearer XYZ"

    return get_capacities_api(powerbi_auth_token_provider=dummy_auth_token_provider)


@pytest.fixture
def management_capacities_api() -> ManagementCapacitiesAPI:
    def dummy_auth_token_provider():
        return "Bearer XYZ"

    class FakeConfig:
        AZURE_SUBSCRIPTION_ID: str = "97bd5d65-c018-4632-b6c3-0feeb064b3d1"
        AZURE_RESOURCE_GROUP_NAME: str = "indiebi-dev"

    return get_management_capacities_api(
        config=FakeConfig(), auth_token_provider=dummy_auth_token_provider
    )


def test_assign_releases_sets_requested_shard_for_missing_shard(
    db_session: Session,
    repo: Repo,
    capacities_api: CapacitiesAPI,
    management_capacities_api: ManagementCapacitiesAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    active_temporary_capacity,
):
    version_factory(id="2.3.0", shard_version="2.3.0")
    permission_set_factory()
    release_factory(version_id="2.3.0")

    assign_releases(
        repo=repo,
        capacities_api=capacities_api,
        management_capacities_api=management_capacities_api,
    )

    all_releases = db_session.query(_DBRelease).all()
    assert all_releases[0].status == ReleaseStatus.REQUESTED_SHARD
    assert len(all_releases) == 1


def test_assign_releases_sets_requested_shard_for_missing_shard_only_for_one_relese_request(
    db_session: Session,
    repo: Repo,
    capacities_api: CapacitiesAPI,
    management_capacities_api: ManagementCapacitiesAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    active_temporary_capacity,
):
    version_factory(id="2.3.0", shard_version="2.3.0")
    permission_set_factory()
    release_factory(version_id="2.3.0", studio_id=1)
    release_factory(version_id="2.3.0", studio_id=2)

    assign_releases(
        repo=repo,
        capacities_api=capacities_api,
        management_capacities_api=management_capacities_api,
    )

    all_releases = db_session.query(_DBRelease).all()
    assert all_releases[0].status == ReleaseStatus.REQUESTED_SHARD
    assert all_releases[0].studio_id == 1
    assert all_releases[1].status == ReleaseStatus.REQUESTED
    assert all_releases[1].studio_id == 2
    assert len(all_releases) == 2


def test_assign_releases_sets_requested_assign_if_shard_exists(
    db_session: Session,
    repo: Repo,
    capacities_api: CapacitiesAPI,
    management_capacities_api: ManagementCapacitiesAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    shard_factory,
    active_temporary_capacity,
):
    version_factory(id="2.3.0", shard_version="2.3.0")
    permission_set_factory()
    release_factory(version_id="2.3.0")
    shard_factory(version="2.3.0")

    assign_releases(
        repo=repo,
        capacities_api=capacities_api,
        management_capacities_api=management_capacities_api,
    )

    all_releases = db_session.query(_DBRelease).all()
    assert all_releases[0].status == ReleaseStatus.REQUESTED_ASSIGN
    assert len(all_releases) == 1


def test_assign_releases_sets_requested_assign_if_shard_exists_for_all_waiting_for_assign(
    db_session: Session,
    repo: Repo,
    capacities_api: CapacitiesAPI,
    management_capacities_api: ManagementCapacitiesAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    shard_factory,
    active_temporary_capacity,
):
    version_factory(id="2.3.0", shard_version="2.3.0")
    permission_set_factory()
    release_factory(version_id="2.3.0", studio_id=1)
    release_factory(version_id="2.3.0", studio_id=2)
    release_factory(version_id="2.3.0", studio_id=3)
    shard_factory(version="2.3.0")

    assign_releases(
        repo=repo,
        capacities_api=capacities_api,
        management_capacities_api=management_capacities_api,
    )

    all_releases = db_session.query(_DBRelease).all()
    assert all_releases[0].status == ReleaseStatus.REQUESTED_ASSIGN

    assert all_releases[0].status == ReleaseStatus.REQUESTED_ASSIGN
    assert all_releases[0].studio_id == 1
    assert all_releases[1].status == ReleaseStatus.REQUESTED_ASSIGN
    assert all_releases[1].studio_id == 2
    assert all_releases[2].status == ReleaseStatus.REQUESTED_ASSIGN
    assert all_releases[2].studio_id == 3
    assert len(all_releases) == 3


def test_assign_releases_sets_requested_shard_for_full_recreate_requested_only_for_first_release(
    db_session: Session,
    repo: Repo,
    capacities_api: CapacitiesAPI,
    management_capacities_api: ManagementCapacitiesAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    shard_factory,
    active_temporary_capacity,
):
    version_factory(id="2.3.0", shard_version="2.3.0")
    permission_set_factory()
    release_factory(version_id="2.3.0", studio_id=1, is_full_recreate=True)
    release_factory(version_id="2.3.0", studio_id=2, is_full_recreate=True)
    shard_factory(version="2.3.0")

    assign_releases(
        repo=repo,
        capacities_api=capacities_api,
        management_capacities_api=management_capacities_api,
    )

    all_releases = db_session.query(_DBRelease).all()

    assert all_releases[0].status == ReleaseStatus.REQUESTED_SHARD
    assert all_releases[0].studio_id == 1
    assert all_releases[1].status == ReleaseStatus.REQUESTED
    assert all_releases[1].studio_id == 2

    assert len(all_releases) == 2


def test_assign_releases_resumes_paused_capacity(
    db_session: Session,
    repo: Repo,
    capacities_api: CapacitiesAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    capacity_factory,
    management_capacities_api: ManagementCapacitiesAPI,
    resume_temoprary_capacity,
    get_details_of_temporary_capacity_to_be_resumed,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release_factory(version_id="2.3.0")

    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        state="Paused",
        is_default_for_releases=True,
    )

    assign_releases(
        repo=repo,
        capacities_api=capacities_api,
        management_capacities_api=management_capacities_api,
    )

    all_capacities = db_session.query(_DBCapacity).all()
    assert all_capacities[0].name == "temporary_capacity_name"
    assert all_capacities[0].state == CapacityState.ACTIVE


def test_handle_requested_shard_creates_permission_set_shard(
    db_session: Session,
    repo: Repo,
    version_factory,
    permission_set_factory,
    release_factory,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    model_container,
    create_permission_set_shard_mock: MagicMock,
    assign_to_best_capacity_mock: MagicMock,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release = release_factory(version_id="2.3.0", status=ReleaseStatus.REQUESTED_SHARD)

    handle_requested_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        environment=Environment("local"),
        processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
        processed_adls_credentials=ProcessedAdlsCredentials(
            "processed_adls_credential"
        ),
        release=Release.model_validate(release),
    )

    create_permission_set_shard_mock.assert_called_once_with(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        request=CreatePermissionSetShardRequest(
            permission_set_uuid=PermissionSetUUID(
                "eeeeeee1-7d79-4403-83e9-916b65129739"
            ),
            shard_version=ShardVersion("2.3.0"),
            environment=Environment("local"),
            processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
            processed_adls_credentials=ProcessedAdlsCredentials(
                "processed_adls_credential"
            ),
        ),
    )
    all_releases = db_session.query(_DBRelease).all()
    assert all_releases[0].try_count == 0, "There were unknown exception"


def test_handle_requested_shard_is_moveding_shard_to_best_capacity_after_refresed(
    repo: Repo,
    version_factory,
    permission_set_factory,
    release_factory,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    model_container,
    create_permission_set_shard_mock: MagicMock,
    assign_to_best_capacity_mock: MagicMock,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release = release_factory(version_id="2.3.0", status=ReleaseStatus.REQUESTED_SHARD)

    handle_requested_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        environment=Environment("local"),
        processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
        processed_adls_credentials=ProcessedAdlsCredentials(
            "processed_adls_credential"
        ),
        release=Release.model_validate(release),
    )

    create_permission_set_shard_mock.assert_called_once()
    assign_to_best_capacity_mock.assert_called_once_with(
        repo=repo,
        workspace_api=workspace_api,
        shard=create_permission_set_shard_mock(),
    )


def test_handle_requested_shard_doesnt_create_studio_profile(
    repo: Repo,
    version_factory,
    permission_set_factory,
    release_factory,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    model_container,
    create_permission_set_shard_mock: MagicMock,
    assign_to_best_capacity_mock: MagicMock,
    assign_to_profile_mock: MagicMock,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release = release_factory(version_id="2.3.0", status=ReleaseStatus.REQUESTED_SHARD)

    handle_requested_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        environment=Environment("local"),
        processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
        processed_adls_credentials=ProcessedAdlsCredentials(
            "processed_adls_credential"
        ),
        release=Release.model_validate(release),
    )
    create_permission_set_shard_mock.assert_called_once()
    assign_to_best_capacity_mock.assert_called_once()
    assign_to_profile_mock.assert_not_called()


def test_handle_requested_shard_sets_release_as_requested_assign(
    db_session: Session,
    repo: Repo,
    version_factory,
    permission_set_factory,
    release_factory,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    model_container,
    create_permission_set_shard_mock: MagicMock,
    assign_to_best_capacity_mock: MagicMock,
    assign_to_profile_mock: MagicMock,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release = release_factory(version_id="2.3.0", status=ReleaseStatus.REQUESTED_SHARD)

    handle_requested_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        environment=Environment("local"),
        processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
        processed_adls_credentials=ProcessedAdlsCredentials(
            "processed_adls_credential"
        ),
        release=Release.model_validate(release),
    )

    create_permission_set_shard_mock.assert_called_once()
    assign_to_best_capacity_mock.assert_called_once()
    assign_to_profile_mock.assert_not_called()

    all_releases = db_session.query(_DBRelease).all()
    assert all_releases[0].status == ReleaseStatus.REQUESTED_ASSIGN
    assert len(all_releases) == 1


def test_handle_requested_assign_assings_to_profile(
    repo: Repo,
    profile_api: ProfileAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    shard_factory,
    assign_to_profile_mock: MagicMock,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release = release_factory(version_id="2.3.0", status=ReleaseStatus.REQUESTED_ASSIGN)
    shard_factory(
        workspace_id="workspace1-ps1-4e18-aea3-4cb4a3a50b48",
        version="2.3.0",
    )

    handle_requested_assign(
        repo=repo,
        profile_api=profile_api,
        environment=Environment("local"),
        service_principal_id=ServicePrincipalId("mainprincipal-123-123-123-33333333"),
        release=Release.model_validate(release),
    )

    assign_to_profile_mock.assert_called_with(
        repo=repo,
        profile_api=profile_api,
        request=AssignToProfileRequest(
            environment=Environment("local"),
            studio_id=StudioId(1),
            version_id=VersionId("2.3.0"),
            permission_set_uuid=PermissionSetUUID(
                "eeeeeee1-7d79-4403-83e9-916b65129739"
            ),
        ),
    )


def test_handle_requested_assign_marks_release_as_done(
    db_session: Session,
    repo: Repo,
    profile_api: ProfileAPI,
    version_factory,
    permission_set_factory,
    release_factory,
    shard_factory,
    assign_to_profile_mock: MagicMock,
):
    version_factory(
        id="2.3.0",
        shard_version="2.3.0",
        visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
        visuals_dataset_id="93db8fdc-37da-4cf1-a34f-3c1c34a4af86",
    )
    permission_set_factory()
    release = release_factory(version_id="2.3.0", status=ReleaseStatus.REQUESTED_ASSIGN)
    shard_factory(
        workspace_id="workspace1-ps1-4e18-aea3-4cb4a3a50b48",
        version="2.3.0",
    )

    handle_requested_assign(
        repo=repo,
        profile_api=profile_api,
        environment=Environment("local"),
        service_principal_id=ServicePrincipalId("mainprincipal-123-123-123-33333333"),
        release=Release.model_validate(release),
    )

    assign_to_profile_mock.assert_called_once()

    all_releases = db_session.query(_DBRelease).all()
    release = all_releases[0]
    assert release.status == ReleaseStatus.DONE
    assert len(all_releases) == 1
