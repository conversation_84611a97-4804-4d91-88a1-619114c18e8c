from sqlalchemy.orm import Session

from dataset_manager.azure.powerbi.profiles import ProfileAPI
from dataset_manager.entities import (
    Environment,
    PermissionSetUUID,
    StudioId,
    VersionId,
)
from dataset_manager.logic.shard import AssignToProfileRequest, assign_to_profile
from dataset_manager.repo import Repo
from dataset_manager.repo.profile import _DB<PERSON>rofile


def test_asign_to_new_profile(
    db_session: Session,
    repo: Repo,
    profile_api: ProfileAPI,
    profile_factory,
    get_profiles_another_existing,
    create_profile_for_studio_1,
):
    assign_to_profile(
        repo=repo,
        profile_api=profile_api,
        request=AssignToProfileRequest(
            environment=Environment("local"),
            studio_id=StudioId(1),
            version_id=VersionId("3.0.0"),
            permission_set_uuid=PermissionSetUUID(
                "eeeeeee1-7d79-4403-83e9-916b65129739",
            ),
        ),
    )

    all_profiles_created = db_session.query(_DBProfile).all()
    profile = all_profiles_created[0]

    assert profile.studio_id == 1
    assert profile.profile_name == "local_1"
    assert profile.profile_id == "profile1-54b7-43f4-b072-ed4c1f9d5824"
    assert str(profile.active_version_id) == "3.0.0"
    assert profile.permission_set_uuid == "eeeeeee1-7d79-4403-83e9-916b65129739"

    assert len(all_profiles_created) == 1


def test_asign_to_existing_profile(
    db_session: Session,
    repo: Repo,
    profile_api: ProfileAPI,
    profile_factory,
):
    profile_factory(
        studio_id=1,
        active_version_id="2.0.0",
        profile_id="profile1-54b7-43f4-b072-ed4c1f9d5824",
        permission_set_uuid="eeeeeee1-7d79-4403-83e9-916b65129739",
    )
    assign_to_profile(
        repo=repo,
        profile_api=profile_api,
        request=AssignToProfileRequest(
            environment=Environment("local"),
            studio_id=StudioId(1),
            version_id=VersionId("3.0.0"),
            permission_set_uuid=PermissionSetUUID(
                "fffffff2-7d79-4403-83e9-916b65129739",
            ),
        ),
    )

    released_studio_profile = (
        db_session.query(_DBProfile).filter(_DBProfile.studio_id == 1).one()
    )
    assert released_studio_profile.active_version_id == "3.0.0"
    assert (
        released_studio_profile.permission_set_uuid
        == "fffffff2-7d79-4403-83e9-916b65129739"
    )
