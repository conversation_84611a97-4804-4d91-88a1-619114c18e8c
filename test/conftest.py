import json
import os
import uuid
from contextlib import contextmanager
from datetime import datetime
from hashlib import md5
from random import random
from typing import Iterator
from unittest.mock import patch

import factory
import pytest
import responses
import respx
from azure.storage.blob import BlobServiceClient
from fastapi.testclient import TestClient
from pydantic import BaseModel
from responses import matchers
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, scoped_session, sessionmaker

from dataset_manager.api.dependencies import (
    get_management_auth_token_provider,
    get_model_container,
    get_powerbi_aio_auth_token_provider,
    get_powerbi_auth_token_provider,
)
from dataset_manager.api.healthcheck import get_container_client
from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.identity import credential
from dataset_manager.azure.powerbi.datasets import get_dataset_api
from dataset_manager.azure.powerbi.profiles import get_profile_api
from dataset_manager.azure.powerbi.workspaces import get_workspace_api
from dataset_manager.config import (
    SQLALCHEMY_DATABASE_URL,
    PBICredentials,
    Settings,
    get_modern_settings,
)
from dataset_manager.connectors.db_engine import get_session, get_session_context
from dataset_manager.connectors.user_service import (
    get_legacy_studio_id_from_user_id_sync,
)
from dataset_manager.main import app
from dataset_manager.repo import Repo, get_repo
from dataset_manager.repo.capacity import _DBCapacity
from dataset_manager.repo.permission_set import _DBPermissionSet
from dataset_manager.repo.profile import _DBProfile
from dataset_manager.repo.release import ReleaseStatus, _DBRelease
from dataset_manager.repo.shard import _DBShard
from dataset_manager.repo.version import _DBVersion


@pytest.fixture(scope="session")
def db_setup():
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
    )
    scoped_session_management = scoped_session(sessionmaker(bind=engine))
    yield scoped_session_management
    scoped_session_management.remove()


@pytest.fixture
def remove_all_azure_credentials():
    current_credentials = credential.credentials
    credential.credentials = []
    yield
    credential.credentials = current_credentials


@pytest.fixture(autouse=True)
def prepare_credential_environment_variables():
    class FakeToken(BaseModel):
        token: str

    class FakeCredential:
        def __init__(self, index):
            self.index = index

        def get_token(self, *scope):
            return FakeToken(token=f"scoped_token_{self.index}")

    class FakeAioCredential:
        def __init__(self, index):
            self.index = index

        async def get_token(self, *scope):
            return FakeToken(token=f"scoped_aio_token_{self.index}")

    def _build_sharded_credentials() -> list[FakeCredential]:
        return [FakeCredential(i) for i in range(16)]

    def _build_aio_sharded_credentials() -> list[FakeAioCredential]:
        return [FakeAioCredential(i) for i in range(16)]

    with (
        patch(
            "dataset_manager.azure.identity._build_sharded_credentials",
            _build_sharded_credentials,
        ),
        patch(
            "dataset_manager.azure.identity._aio_build_sharded_credentials",
            _build_aio_sharded_credentials,
        ),
    ):
        yield


@pytest.fixture
def models_container_name():
    return f"models{str(random()).replace('.', '')}"


@pytest.fixture
def blob_service_client(responses_mock, models_container_name):
    azurite_host = os.environ.get("AZURITE_HOST", "localhost")
    print("AZURITE_HOST", azurite_host)
    responses_mock.add_passthru(f"http://{azurite_host}:10000")
    azurite_emulator_connection = f"DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://{azurite_host}:10000/devstoreaccount1;"

    # Crete container for Azurite for the first run
    blob_service_client = BlobServiceClient.from_connection_string(
        azurite_emulator_connection
    )
    try:
        blob_service_client.create_container(models_container_name)
    except Exception as e:
        print(e)

    yield blob_service_client

    blob_service_client.delete_container(models_container_name)


@pytest.fixture
def models_client(blob_service_client, models_container_name):
    return blob_service_client.get_container_client(container=models_container_name)


@pytest.fixture
def model_container(blob_service_client, models_container_name) -> ModelContainer:
    return ModelContainer(
        client=blob_service_client, container_name=models_container_name
    )


@pytest.fixture
def settings():
    pbi_credentials = [
        PBICredentials(
            index=i,
            client_id=f"test-client-id-{i}",
            client_secret=f"test-secret-{i}",
            object_id=f"test-object-id-{i}",
        )
        for i in range(16)
    ]

    return Settings(
        env="test",
        pbi_credentials=pbi_credentials,
        pbi_azure_tenant_id="test-tenant-id",
    )


@pytest.fixture
def client(model_container, settings) -> Iterator[TestClient]:
    def dummy_auth_token_provider():
        return lambda: "Bearer default_token"

    def aio_dummy_auth_token_provider():
        async def _get_token():
            return "Bearer default_aio_token"

        return _get_token

    def get_azurite_model_container():
        return model_container

    old_deps = app.dependency_overrides.copy()

    app.dependency_overrides[get_management_auth_token_provider] = (
        dummy_auth_token_provider
    )
    app.dependency_overrides[get_powerbi_auth_token_provider] = (
        dummy_auth_token_provider
    )
    app.dependency_overrides[get_powerbi_aio_auth_token_provider] = (
        aio_dummy_auth_token_provider
    )

    app.dependency_overrides[get_model_container] = get_azurite_model_container
    app.dependency_overrides[get_modern_settings] = lambda: settings

    yield TestClient(app)

    app.dependency_overrides = old_deps


@pytest.fixture
def authorized_client(client, authorization_header):
    return TestClient(app, headers=authorization_header)


@pytest.fixture(scope="session")
def authorization_header():
    return {"x-api-key": "pikachu"}


@pytest.fixture
def db_session(db_setup, model_container) -> Iterator[Session]:
    session: Session = db_setup()

    def override_get_db():
        yield session

    def override_session_context():
        @contextmanager
        def _session_contex():
            yield session

        return _session_contex

    old_deps = app.dependency_overrides.copy()

    app.dependency_overrides[get_session] = override_get_db
    app.dependency_overrides[get_session_context] = override_session_context

    yield session

    app.dependency_overrides = old_deps
    session.rollback()


@pytest.fixture
def repo(db_session) -> Repo:
    return get_repo(session=db_session)


@pytest.fixture
def capacity_factory(db_session) -> type[factory.Factory]:
    class _CapacityFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = _DBCapacity
            sqlalchemy_session = db_session

        id = "xxx-xxx-capacity"
        name = "activecapacity"
        tier = "A2"
        state = "Active"
        is_default = False
        is_default_for_releases = False

    return _CapacityFactory


@pytest.fixture
def profile_factory(db_session) -> type[factory.Factory]:
    class _ProfileFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = _DBProfile
            sqlalchemy_session = db_session

        profile_id = factory.LazyAttribute(lambda _: str(uuid.uuid4()))  # TODO: lazy it
        profile_name = factory.LazyAttribute(lambda o: f"local_{o.studio_id}")
        studio_id = factory.Sequence(lambda n: n + 1)
        creation_timestamp = datetime(2022, 8, 8, 10, 0, 0, 0)
        deletion_timestamp = None
        active_version_id = "2.0.4"
        permission_set_uuid = "eeeeeee1-7d79-4403-83e9-916b65129739"

    return _ProfileFactory


@pytest.fixture
def shard_factory(db_session) -> type[factory.Factory]:
    class _ProfileFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = _DBShard
            sqlalchemy_session = db_session

        version = "v1"
        dataset_id = "11111111-1758-40f1-bf7d-13880a5c3ce5"
        dataset_name = None
        workspace_id = "*************-40f1-bf7d-13880a5c3ce5"
        workspace_name = "sample_workspace_name"
        capacity_id = "*************-40f1-bf7d-13880a5c3ce5"
        creation_timestamp = datetime(2022, 8, 8, 10, 0, 0, 0)
        permission_set_uuid = "eeeeeee1-7d79-4403-83e9-916b65129739"
        last_refresh_timestamp = datetime(2022, 8, 8, 10, 10, 10, 0)

    return _ProfileFactory


@pytest.fixture
def permission_set_factory(db_session) -> type[factory.Factory]:
    class _PermissionSetFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = _DBPermissionSet
            sqlalchemy_session = db_session

        uuid = factory.Sequence(lambda n: f"eeeeeee{n + 1}-7d79-4403-83e9-916b65129739")
        permission_set = factory.Sequence(
            lambda n: [{"studio_id": n + 1, "product_name": None}]
        )
        permission_set_hash = factory.LazyAttribute(
            lambda o: md5(json.dumps(o.permission_set).encode()).hexdigest()
        )

    return _PermissionSetFactory


@pytest.fixture
def release_factory(db_session) -> type[factory.Factory]:
    class _ReleaseFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = _DBRelease
            sqlalchemy_session = db_session

        uuid = factory.LazyAttributeSequence(
            lambda o, n: f"eeae{o.studio_id:04d}-{n:04d}-4b9a-9a61-b656621d606b"
        )
        studio_id = 1
        version_id = "2.0.4"
        permission_set_uuid = "eeeeeee1-7d79-4403-83e9-916b65129739"
        creation_timestamp = datetime(2022, 12, 20, 15, 20)
        status = ReleaseStatus.REQUESTED
        is_full_recreate = False
        try_count = 0

    return _ReleaseFactory


@pytest.fixture
def version_factory(db_session) -> type[factory.Factory]:
    class _VersionFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = _DBVersion
            sqlalchemy_session = db_session

        id = "2.0.4"
        shard_version = "v1"
        shard_template_workspace_id = "shard-template-workspace-bf7d-13880a5c3c"
        visuals_workspace_id = "visuals-1234-5678-123123123"
        visuals_dataset_id = "dataset-1234-5678-123123123"
        visuals_reports_ids = factory.List(
            [
                "report01-aaaa-bbbb-cccc-ddddddcccccc",
                "report02-aaaa-bbbb-cccc-ddddddcccccc",
                "report03-aaaa-bbbb-cccc-ddddddcccccc",
            ]
        )
        visuals_desktop_definitions = factory.Dict(
            {
                "version": "2.0.4",
                "last_updated": "2022-09-07T11:51:06.042931",
                "menu_items": [
                    {
                        "id": "c069714b-0fdb-416d-a145-ef473be8c9c2",
                        "type": "powerbi",
                        "icon": "trophy-outline",
                        "display_name": "Top products",
                        "internal_name": "Top Products",
                        "pbix_name": "Top Products",
                        "page_name": "ReportSection015a5759087d2bcc815b",
                        "visual_container_id": "191997966853de58c006",
                        "description": {
                            "title": "Your top products",
                            "description": "A simple, summarized view of your best selling products.",
                            "features": [
                                "See your top 10 products in a given country and region",
                                "Check the percentage of your best products in total sales",
                            ],
                        },
                        "dashboard_url": "https://app.powerbi.com/reportEmbed?reportId=7350235f-5c22-4bec-9bbf-0c3b7d99dbb8&groupId=07d60db4-cc0e-4ca0-9f3e-64e69b025d3b&language=en&formatLocale=en-US",
                    }
                ],
            }
        )
        is_active = True
        is_default = True

    return _VersionFactory


@pytest.fixture
def responses_mock(remove_all_azure_credentials):
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture
def workspace_api():
    def dummy_auth_token_provider():
        return "Bearer XYZ"

    return get_workspace_api(auth_token_provider=dummy_auth_token_provider)


@pytest.fixture
def profile_api():
    def dummy_auth_token_provider():
        return "Bearer XYZ"

    return get_profile_api(auth_token_provider=dummy_auth_token_provider)


@pytest.fixture
def dataset_api():
    def dummy_auth_token_provider():
        return "Bearer XYZ"

    return get_dataset_api(auth_token_provider=dummy_auth_token_provider)


@pytest.fixture
def get_management_details_of_capacity(responses_mock: responses.RequestsMock):
    def _get_management_details_of_capacity(
        capacity_name: str = "activecapacity",
        capacity_tier: str = "A2",
        state: str = "Succeeded",
    ):
        responses_mock.get(
            f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
            json={
                "properties": {
                    "mode": "Gen2",
                    "provisioningState": "Succeeded",
                    "state": state,
                    "administration": {"members": ["<EMAIL>"]},
                },
                "id": f"/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}",
                "name": capacity_name,
                "type": "Microsoft.PowerBIDedicated/capacities",
                "location": "West Europe",
                "sku": {"name": capacity_tier, "tier": "PBIE_Azure", "capacity": 1},
                "tags": {"env": "dev", "purpose": "releases", "team": "data"},
            },
            match=[matchers.header_matcher({"Authorization": "Bearer default_token"})],
        )

    return _get_management_details_of_capacity


@pytest.fixture
def existing_customer(
    version_factory, profile_factory, shard_factory, permission_set_factory
):
    version_factory()
    profile_factory()
    shard_factory()
    permission_set_factory()


@pytest.fixture(scope="function")
def user_service_legacy_id_lookup_mock():
    with respx.mock(assert_all_called=True, assert_all_mocked=True) as respx_mock:
        get_legacy_studio_id_from_user_id_sync.cache_clear()
        respx_mock.get(url="https://user-service/user/u-aBcDeF").respond(
            json={"email": "<EMAIL>", "id": "u-aBcDeF", "legacy_id": 1}
        )
        print("Enter")
        yield
        print("Exit")


@pytest.fixture
def user_service_legacy_id_nonexistent_mock(respx_mock: respx.MockRouter):
    respx_mock.get(url="https://user-service/user/u-aBcDeF").respond(404)


@pytest.fixture
def dim_portals_query_mock(respx_mock):
    query = """
            EVALUATE SELECTCOLUMNS(dim_portals,
            "portal_platform_region", [portal_platform_region],
            "portal", [portal],
            "platform", [platform],
            "region", [region],
            "store", [store],
            "abbreviated_name", [abbreviated_name]
            )"""
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/*************-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/executeQueries",
        json={
            "queries": [{"query": query}],
            "serializerSettings": {"includeNulls": True},
        },
    ).respond(
        json={
            "results": [
                {
                    "tables": [
                        {
                            "rows": [
                                {
                                    "[portal_platform_region]": "Steam:PC:Global",
                                    "[portal]": "Steam",
                                    "[platform]": "PC",
                                    "[region]": "Global",
                                    "[store]": "Steam",
                                    "[abbreviated_name]": "Steam",
                                },
                                {
                                    "[portal_platform_region]": "Meta:Rift:Global",
                                    "[portal]": "Meta",
                                    "[platform]": "Rift",
                                    "[region]": "Global",
                                    "[store]": "Meta Rift",
                                    "[abbreviated_name]": "Rift",
                                },
                                {
                                    "[portal_platform_region]": "Meta:Quest:Global",
                                    "[portal]": "Meta",
                                    "[platform]": "Quest",
                                    "[region]": "Global",
                                    "[store]": "Meta Quest",
                                    "[abbreviated_name]": "Quest",
                                },
                            ]
                        }
                    ]
                }
            ]
        }
    )


@pytest.fixture
def dim_sku_query_mock(respx_mock):
    query = """
            EVALUATE SELECTCOLUMNS(dim_sku,
            "unique_sku_id", [unique_sku_id],
            "base_sku_id", [base_sku_id],
            "portal_platform_region", [portal_platform_region],
            "gso", [gso],
            "human_name", [human_name],
            "product_name", RELATED(dim_products[product_name]),
            "sku_type", [sku_type],
            "studio_id", RELATED(dim_products[studio_id]),
            "product_id", [product_id],
            "release_date", [release_date])
            """
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/*************-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/executeQueries",
        json={
            "queries": [{"query": query}],
            "serializerSettings": {"includeNulls": True},
        },
    ).respond(
        json={
            "results": [
                {
                    "tables": [
                        {
                            "rows": [
                                {
                                    "[unique_sku_id]": "42ccb715787e47f48648159060135ef1-epic:2",
                                    "[base_sku_id]": "42ccb715787e47f48648159060135ef1",
                                    "[portal_platform_region]": "Epic:PC:Global",
                                    "[gso]": 11,
                                    "[human_name]": "SUPERHOT",
                                    "[product_name]": "SUPERHOT",
                                    "[sku_type]": "SALES",
                                    "[studio_id]": 2,
                                    "[product_id]": "SUPERHOT:101010:2",
                                    "[release_date]": "2014-06-14T00:00:00",
                                },
                                {
                                    "[unique_sku_id]": "942e9424124145fca7fadefefb9b70e6-epic:2",
                                    "[base_sku_id]": "942e9424124145fca7fadefefb9b70e6",
                                    "[portal_platform_region]": "Epic:PC:Global",
                                    "[gso]": 12,
                                    "[human_name]": "SUPERHOT: Mind Control Delete",
                                    "[product_name]": "SUPERHOT MCD",
                                    "[sku_type]": "SALES",
                                    "[studio_id]": 2,
                                    "[product_id]": "SUPERHOT MCD:101010:2",
                                    "[release_date]": "2014-06-14T00:00:00",
                                },
                            ]
                        }
                    ]
                }
            ]
        }
    )


@pytest.fixture(autouse=True)
def mock_sleep_on_retry_deploy_dataset_old():
    with patch("dataset_manager.azure.powerbi.imports.sleep"):
        yield


@pytest.fixture(autouse=True)
def mock_sleep_on_retry_deploy_dataset():
    with patch("dataset_manager.azure.powerbi.datasets.sleep"):
        yield


@pytest.fixture(autouse=True)
def mock_sleep_on_capacity_logic():
    with patch("dataset_manager.logic.capacity.sleep"):
        yield


@pytest.fixture(autouse=True)
def mock_sleep_on_shard_logic():
    with patch("dataset_manager.logic.shard.sleep") as m:
        yield m


@pytest.fixture
def container_client():
    class _ContainerClient:
        def __init__(self):
            self._exception = None
            self._exists = True

        @property
        def exception(self):
            return self._exception

        @exception.setter
        def exception(self, exception):
            self._exception = exception

        @property
        def container_exists(self):
            return self._exists

        @container_exists.setter
        def container_exists(self, value):
            self._exists = value

        def exists(self, *args, **kwds):
            if self._exception:
                raise self._exception
            return self._exists

    container_client = _ContainerClient()

    def override_get_client():
        yield container_client

    app.dependency_overrides[get_container_client] = override_get_client
    yield container_client
    app.dependency_overrides = {}
