import datetime

import pytest
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from dataset_manager.connectors.user_service import (
    get_legacy_studio_id_from_user_id_sync,
)
from dataset_manager.repo.profile import _DBProfile


@pytest.fixture(autouse=True)
def clear_cache():
    get_legacy_studio_id_from_user_id_sync.cache_clear()


def test_delete_user_nonexistent_user_returns_404(
    client,
    authorization_header,
    user_service_legacy_id_nonexistent_mock,
):
    response = client.delete(
        "/user/u-aBcDeF",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_delete_user_deletes_profile_from_pbi_and_marks_as_deleted_in_db(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    responses_mock,
    user_service_legacy_id_lookup_mock,
):
    profile_factory(studio_id=1, profile_id="test-profile-id-123")
    responses_mock.delete(
        "https://api.powerbi.com/v1.0/myorg/profiles/test-profile-id-123"
    )
    response = client.delete(
        "/user/u-aBcDeF",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK

    response_data = response.json()
    assert response_data["studio_id"] == 1
    assert response_data["profile_id"] == "test-profile-id-123"
    assert response_data["deletion_timestamp"] is not None

    db_profile = db_session.query(_DBProfile).filter_by(studio_id=1).first()
    assert db_profile is not None
    assert db_profile.deletion_timestamp is not None


def test_delete_user_already_deleted_profile_returns_404(
    client,
    authorization_header,
    profile_factory,
    user_service_legacy_id_lookup_mock,
):
    profile_factory(
        studio_id=1,
        profile_id="deleted-profile",
        deletion_timestamp=datetime.datetime.now(datetime.UTC),
    )
    response = client.delete(
        "/user/u-aBcDeF",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}
