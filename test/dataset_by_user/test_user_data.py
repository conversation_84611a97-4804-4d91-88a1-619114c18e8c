import pytest
from sqlalchemy.orm import Session
from starlette.status import (
    HTTP_200_OK,
    HTTP_404_NOT_FOUND,
    HTTP_503_SERVICE_UNAVAILABLE,
)

from dataset_manager.connectors.user_service import (
    get_legacy_studio_id_from_user_id_sync,
)


def test_not_existing_customer_returns_404_sku(
    db_session: Session,
    client,
    authorization_header,
    user_service_legacy_id_nonexistent_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_not_existing_customer_returns_404_portal(
    db_session: Session,
    client,
    authorization_header,
    user_service_legacy_id_nonexistent_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/portal",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_unavailable_user_service_returns_503(
    db_session: Session, client, authorization_header, respx_mock
):
    get_legacy_studio_id_from_user_id_sync.cache_clear()
    respx_mock.get(url="https://user-service/user/u-aBcDeF").respond(500)

    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_503_SERVICE_UNAVAILABLE
    assert response.json() == {"detail": "User Service not available"}


@pytest.fixture
def instance_is_overloaded(respx_mock):
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/executeQueries",
    ).respond(
        status_code=429,
        json={
            "error": {
                "code": "DatasetExecuteQueriesError",
                "pbi.error": {
                    "code": "DatasetExecuteQueriesError",
                    "parameters": {},
                    "details": [
                        {
                            "code": "DetailsMessage",
                            "detail": {
                                "type": 1,
                                "value": "The database was evicted and the operation cancelled to load balance the CPU load on the node. Please try again later.",
                            },
                        },
                        {
                            "code": "AnalysisServicesErrorCode",
                            "detail": {"type": 1, "value": "3238068279"},
                        },
                    ],
                },
            }
        },
    )


@pytest.fixture
def instance_is_reaches_limit(respx_mock):
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/executeQueries",
    ).respond(
        status_code=429,
        json={
            "error": {
                "code": "DatasetExecuteQueriesError",
                "pbi.error": {
                    "code": "DatasetExecuteQueriesError",
                    "parameters": {},
                    "details": [
                        {
                            "code": "DetailsMessage",
                            "detail": {
                                "type": 1,
                                "value": "Unable to complete the action because your organization's Fabric compute capacity has exceeded its limits. Try again later. See https://go.microsoft.com/fwlink/?linkid=2247526 to learn more.",
                            },
                        },
                        {
                            "code": "AnalysisServicesErrorCode",
                            "detail": {"type": 1, "value": "3238789217"},
                        },
                    ],
                },
            }
        },
    )


@pytest.fixture
def unknow_pbi_error(respx_mock):
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/executeQueries",
    ).respond(
        status_code=429,
        json={
            "unknow_format_hahha :)": "You shall not parese this!",
        },
    )


@pytest.fixture
def cant_connect_to_instance(respx_mock):
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/executeQueries",
    ).respond(
        status_code=400,
        json={
            "error": {
                "code": "DatasetExecuteQueriesError",
                "pbi.error": {
                    "code": "DatasetExecuteQueriesError",
                    "parameters": {},
                    "details": [
                        {
                            "code": "DetailsMessage",
                            "detail": {
                                "type": 1,
                                "value": "Failed to open the MSOLAP connection.",
                            },
                        },
                        {
                            "code": "AnalysisServicesErrorCode",
                            "detail": {"type": 1, "value": "0"},
                        },
                    ],
                },
            }
        },
    )


def test_existing_customer_returns_sku(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_sku_query_mock,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "unique_sku_id": "42ccb715787e47f48648159060135ef1-epic:2",
            "base_sku_id": "42ccb715787e47f48648159060135ef1",
            "portal_platform_region": "Epic:PC:Global",
            "gso": 11,
            "name": "SUPERHOT",
            "sku_type": "SALES",
            "studio_id": 2,
            "product_id": "SUPERHOT:101010:2",
            "product_name": "SUPERHOT",
            "release_date": "2014-06-14T00:00:00",
        },
        {
            "unique_sku_id": "942e9424124145fca7fadefefb9b70e6-epic:2",
            "base_sku_id": "942e9424124145fca7fadefefb9b70e6",
            "portal_platform_region": "Epic:PC:Global",
            "gso": 12,
            "name": "SUPERHOT: Mind Control Delete",
            "sku_type": "SALES",
            "studio_id": 2,
            "product_id": "SUPERHOT MCD:101010:2",
            "product_name": "SUPERHOT MCD",
            "release_date": "2014-06-14T00:00:00",
        },
    ]


def test_unknown_pbi_error_returns_409_for_sku(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    unknow_pbi_error,
    caplog,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == 409
    assert response.json() == {
        "error_code": "PBI_UNKNOWN_ERROR",
        "detail": "Unknown PowerBI error",
    }
    assert (
        'Unsupported PBI API error. Status code: 429, body: {"unknow_format_hahha :)":"You shall not parese this!"}'
        in caplog.text
    )


def test_overloaded_instance_returns_409_for_sku(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    instance_is_overloaded,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == 409
    assert response.json() == {
        "error_code": "PBI_INSTANCE_OVERLOADED",
        "detail": "The database was evicted and the operation cancelled to load balance the CPU load on the node. Please try again later.",
    }


def test_limit_on_instance_returns_409_for_sku(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    instance_is_reaches_limit,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == 409
    assert response.json() == {
        "error_code": "PBI_INSTANCE_OVERLOADED",
        "detail": "Unable to complete the action because your organization's Fabric compute capacity has exceeded its limits. Try again later. See https://go.microsoft.com/fwlink/?linkid=2247526 to learn more.",
    }


def test_cant_connect_to_instance_returns_409_for_sku(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    cant_connect_to_instance,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/sku",
        headers=authorization_header,
    )
    assert response.status_code == 409
    assert response.json() == {
        "error_code": "PBI_CANT_CONNECT_INSTANCE",
        "detail": "Failed to open the MSOLAP connection.",
    }


def test_existing_customer_returns_portals(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_portals_query_mock,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/portal",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "portal_platform_region": "Steam:PC:Global",
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "store": "Steam",
            "abbreviated_name": "Steam",
        },
        {
            "portal_platform_region": "Meta:Rift:Global",
            "portal": "Meta",
            "platform": "Rift",
            "region": "Global",
            "store": "Meta Rift",
            "abbreviated_name": "Rift",
        },
        {
            "portal_platform_region": "Meta:Quest:Global",
            "portal": "Meta",
            "platform": "Quest",
            "region": "Global",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
        },
    ]


def test_base64_query_endpoint_returns_requested_sku_data(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_sku_query_mock,
    user_service_legacy_id_lookup_mock,
):
    response = client.post(
        "/dataset/by-user/u-aBcDeF/query",
        headers=authorization_header,
        json={
            "base64_query": "CiAgICAgICAgICAgIEVWQUxVQVRFIFNFTEVDVENPTFVNTlMoZGltX3NrdSwKICAgICAgICAgICAgInVuaXF1ZV9za3VfaWQiLCBbdW5pcXVlX3NrdV9pZF0sCiAgICAgICAgICAgICJiYXNlX3NrdV9pZCIsIFtiYXNlX3NrdV9pZF0sCiAgICAgICAgICAgICJwb3J0YWxfcGxhdGZvcm1fcmVnaW9uIiwgW3BvcnRhbF9wbGF0Zm9ybV9yZWdpb25dLAogICAgICAgICAgICAiZ3NvIiwgW2dzb10sCiAgICAgICAgICAgICJodW1hbl9uYW1lIiwgW2h1bWFuX25hbWVdLAogICAgICAgICAgICAicHJvZHVjdF9uYW1lIiwgUkVMQVRFRChkaW1fcHJvZHVjdHNbcHJvZHVjdF9uYW1lXSksCiAgICAgICAgICAgICJza3VfdHlwZSIsIFtza3VfdHlwZV0sCiAgICAgICAgICAgICJzdHVkaW9faWQiLCBSRUxBVEVEKGRpbV9wcm9kdWN0c1tzdHVkaW9faWRdKSwKICAgICAgICAgICAgInByb2R1Y3RfaWQiLCBbcHJvZHVjdF9pZF0sCiAgICAgICAgICAgICJyZWxlYXNlX2RhdGUiLCBbcmVsZWFzZV9kYXRlXSkKICAgICAgICAgICAg"
        },
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "[unique_sku_id]": "42ccb715787e47f48648159060135ef1-epic:2",
            "[base_sku_id]": "42ccb715787e47f48648159060135ef1",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 11,
            "[human_name]": "SUPERHOT",
            "[product_name]": "SUPERHOT",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
        {
            "[unique_sku_id]": "942e9424124145fca7fadefefb9b70e6-epic:2",
            "[base_sku_id]": "942e9424124145fca7fadefefb9b70e6",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 12,
            "[human_name]": "SUPERHOT: Mind Control Delete",
            "[product_name]": "SUPERHOT MCD",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT MCD:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
    ]


def test_raw_query_endpoint_returns_requested_sku_data(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_sku_query_mock,
    user_service_legacy_id_lookup_mock,
):
    response = client.post(
        "/dataset/by-user/u-aBcDeF/query",
        headers=authorization_header,
        json={
            "raw_query": """
            EVALUATE SELECTCOLUMNS(dim_sku,
            "unique_sku_id", [unique_sku_id],
            "base_sku_id", [base_sku_id],
            "portal_platform_region", [portal_platform_region],
            "gso", [gso],
            "human_name", [human_name],
            "product_name", RELATED(dim_products[product_name]),
            "sku_type", [sku_type],
            "studio_id", RELATED(dim_products[studio_id]),
            "product_id", [product_id],
            "release_date", [release_date])
            """
        },
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "[unique_sku_id]": "42ccb715787e47f48648159060135ef1-epic:2",
            "[base_sku_id]": "42ccb715787e47f48648159060135ef1",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 11,
            "[human_name]": "SUPERHOT",
            "[product_name]": "SUPERHOT",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
        {
            "[unique_sku_id]": "942e9424124145fca7fadefefb9b70e6-epic:2",
            "[base_sku_id]": "942e9424124145fca7fadefefb9b70e6",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 12,
            "[human_name]": "SUPERHOT: Mind Control Delete",
            "[product_name]": "SUPERHOT MCD",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT MCD:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
    ]


def test_query_endpoint_returns_requested_portal_data(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_portals_query_mock,
    user_service_legacy_id_lookup_mock,
):
    response = client.post(
        "/dataset/by-user/u-aBcDeF/query",
        headers=authorization_header,
        json={
            "base64_query": "CiAgICAgICAgICAgIEVWQUxVQVRFIFNFTEVDVENPTFVNTlMoZGltX3BvcnRhbHMsCiAgICAgICAgICAgICJwb3J0YWxfcGxhdGZvcm1fcmVnaW9uIiwgW3BvcnRhbF9wbGF0Zm9ybV9yZWdpb25dLAogICAgICAgICAgICAicG9ydGFsIiwgW3BvcnRhbF0sCiAgICAgICAgICAgICJwbGF0Zm9ybSIsIFtwbGF0Zm9ybV0sCiAgICAgICAgICAgICJyZWdpb24iLCBbcmVnaW9uXSwKICAgICAgICAgICAgInN0b3JlIiwgW3N0b3JlXSwKICAgICAgICAgICAgImFiYnJldmlhdGVkX25hbWUiLCBbYWJicmV2aWF0ZWRfbmFtZV0KICAgICAgICAgICAgKQ=="
        },
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "[portal_platform_region]": "Steam:PC:Global",
            "[portal]": "Steam",
            "[platform]": "PC",
            "[region]": "Global",
            "[store]": "Steam",
            "[abbreviated_name]": "Steam",
        },
        {
            "[portal_platform_region]": "Meta:Rift:Global",
            "[portal]": "Meta",
            "[platform]": "Rift",
            "[region]": "Global",
            "[store]": "Meta Rift",
            "[abbreviated_name]": "Rift",
        },
        {
            "[portal_platform_region]": "Meta:Quest:Global",
            "[portal]": "Meta",
            "[platform]": "Quest",
            "[region]": "Global",
            "[store]": "Meta Quest",
            "[abbreviated_name]": "Quest",
        },
    ]
