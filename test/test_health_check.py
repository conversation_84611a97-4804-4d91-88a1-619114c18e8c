from unittest import mock

import pytest
import responses
from azure.core.exceptions import AzureError
from fastapi import status
from sqlalchemy.exc import SQLAlchemyError


@pytest.fixture
def list_capacities_ok(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities", json={"value": []}
    )


@pytest.fixture
def list_capacities_failed(responses_mock: responses.RequestsMock):
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=500)


@pytest.fixture
def list_management_capacities_ok(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities?api-version=2021-01-01",
        json={"value": []},
    )


@pytest.fixture
def list_management_capacities_failed(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities?api-version=2021-01-01",
        status=500,
    )


@pytest.mark.usefixtures("db_session", "container_client")
def test_synthetic_health_check_returns_good_status_when_services_are_connected(
    authorized_client,
    list_capacities_ok,
    list_management_capacities_ok,
):
    response = authorized_client.get("/health/synthetic")

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "container_client")
@mock.patch("dataset_manager.api.healthcheck.select")
def test_synthetic_health_check_returns_database_not_connected_on_db_error(
    mock_select,
    authorized_client,
    list_capacities_ok,
    list_management_capacities_ok,
):
    mock_select.side_effect = SQLAlchemyError()

    response = authorized_client.get("/health/synthetic")

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is False
    assert response.json()["database_duration"] is None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session")
def test_synthetic_health_check_returns_storage_not_connected_when_container_does_not_exist(
    authorized_client,
    container_client,
    list_capacities_ok,
    list_management_capacities_ok,
):
    container_client.container_exists = False

    response = authorized_client.get("/health/synthetic")

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is False
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session")
def test_synthetic_health_check_returns_storage_not_connected_when_storage_returns_an_error(
    authorized_client,
    container_client,
    list_capacities_ok,
    list_management_capacities_ok,
):
    container_client.exception = AzureError("Error")

    response = authorized_client.get("/health/synthetic")

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is False
    assert response.json()["storage_duration"] is None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "container_client")
def test_synthetic_health_check_returns_pbi_api_not_available_when_pbi_api_does_not_respond(
    authorized_client,
    list_capacities_failed,
    list_management_capacities_ok,
):
    response = authorized_client.get("/health/synthetic")

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is False
    assert response.json()["pbi_api_duration"] is None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "container_client")
def test_synthetic_health_check_returns_azure_api_not_available_when_azure_api_does_not_respond(
    authorized_client,
    list_capacities_ok,
    list_management_capacities_failed,
):
    response = authorized_client.get("/health/synthetic")

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is False
    assert response.json()["azure_api_duration"] is None
