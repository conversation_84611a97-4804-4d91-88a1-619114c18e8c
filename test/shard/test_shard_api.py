from sqlalchemy.orm import Session
from starlette.status import (
    HTTP_200_OK,
    HTTP_202_ACCEPTED,
    HTTP_404_NOT_FOUND,
)


def test_get_shard_on_empty_studio_id_list_returns_all_shards(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    profile_factory,
    version_factory,
):
    profile_factory()
    version_factory()
    shard_factory()

    response = client.get(
        "/shard/active",
        headers=authorization_header,
        params={"studio_ids": []},
    )

    assert response.status_code == 200
    assert response.json() == [
        {
            "version": "v1",
            "dataset_id": "11111111-1758-40f1-bf7d-13880a5c3ce5",
            "dataset_name": None,
            "workspace_id": "*************-40f1-bf7d-13880a5c3ce5",
            "workspace_name": "sample_workspace_name",
            "capacity_id": "*************-40f1-bf7d-13880a5c3ce5",
            "creation_timestamp": "2022-08-08T10:00:00",
            "permission_set_uuid": "eeeeeee1-7d79-4403-83e9-916b65129739",
            "last_refresh_timestamp": "2022-08-08T10:10:10",
        }
    ]


def test_get_shard_with_studio_id_param_returns_shard_for_that_studios(
    db_session: Session,
    client,
    authorization_header,
    permission_set_factory,
    shard_factory,
    profile_factory,
    version_factory,
):
    version_factory()
    version_factory(id="bad_id", shard_version="bad_version")

    permission_set_factory()
    permission_set_2 = permission_set_factory()
    profile_factory()
    shard_factory()
    shard_factory(workspace_id="bad-1758-40f1-bf7d-13880a5c3ce5", version="bad_version")

    profile_factory(
        profile_id="wb9cdg98-o123-izzi-b60h-22222222222",
        profile_name="dev_2",
        studio_id=2,
        permission_set_uuid=permission_set_2.uuid,
    )

    shard_factory(
        dataset_id="*************-40f1-bf7d-13880a5c3ce5",
        workspace_id="*************-40f1-bf7d-13880a5c3ce5",
        workspace_name="workspace_usr_2",
        capacity_id="*************-40f1-bf7d-13880a5c3ce5",
        permission_set_uuid=permission_set_2.uuid,
    )

    profile_factory(
        profile_id="asdfsq-o123-izzi-b60h-22222222222",
        profile_name="dev_3",
        studio_id=3,
    )

    response = client.get(
        "/shard/active",
        headers=authorization_header,
        params={"studio_ids": [1, 2, 3]},
    )
    data = response.json()

    assert len(data) == 2, data
    assert data[0]["workspace_id"] == "*************-40f1-bf7d-13880a5c3ce5"
    assert data[1]["workspace_id"] == "*************-40f1-bf7d-13880a5c3ce5"


def test_assign_shard_to_capacity(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    capacity_factory,
    assign_capacity_to_chosen_one,
):
    shard_factory()
    capacity_factory()
    response = client.put(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/assign-to-capacity",
        headers=authorization_header,
        json={"capacity_name": "activecapacity"},
    )
    data = response.json()
    assert data["capacity_id"] == "xxx-xxx-capacity"


def test_assign_shard_to_capacity_if_capacity_not_added(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
):
    shard_factory()
    response = client.put(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/assign-to-capacity",
        headers=authorization_header,
        json={"capacity_name": "activecapacity"},
    )
    assert response.status_code == HTTP_404_NOT_FOUND


def test_assign_shard_to_capacity_if_shard_not_exists(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
):
    capacity_factory()
    response = client.put(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/assign-to-capacity",
        headers=authorization_header,
        json={"capacity_name": "activecapacity"},
    )
    assert response.status_code == HTTP_404_NOT_FOUND


def test_add_debug_admins(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    assign_debug_admins_to_shard,
    configure_debug_admins,
):
    shard_factory()
    response = client.put(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/add-debug-admins",
        headers=authorization_header,
    )
    response.raise_for_status()


def test_refresh_shard_status_completed(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    dataset_refresh_is_finished,
):
    shard_factory()
    response = client.get(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh-status",
        headers=authorization_header,
    )
    assert response.json()["status"] == "Completed"


def test_refresh_shard_status_saves_refresh_timestamp_in_db(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    profile_factory,
    version_factory,
    permission_set_factory,
    dataset_refresh_is_finished,
):
    permission_set_factory()
    profile_factory()
    version_factory()
    shard_factory()

    response = client.get(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh-status",
        headers=authorization_header,
    )
    assert response.json()["status"] == "Completed"

    response = client.get(
        "/shard/active",
        headers=authorization_header,
        params={"studio_ids": [1]},
    )
    assert response.json()[0]["last_refresh_timestamp"] == "2017-06-13T09:31:43.153000"


def test_refresh_shard_status_does_not_save_incomplete_refresh(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    profile_factory,
    version_factory,
    permission_set_factory,
    dataset_refresh_is_pending,
):
    permission_set_factory()
    profile_factory()
    version_factory()
    shard_factory()
    response = client.get(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh-status",
        headers=authorization_header,
    )
    assert response.json()["status"] == "Unknown"

    response = client.get(
        "/shard/active",
        headers=authorization_header,
        params={"studio_ids": [1]},
    )
    assert response.json()[0]["last_refresh_timestamp"] == "2022-08-08T10:10:10"


def test_refresh_shard_status_is_refreshing(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    dataset_refresh_is_pending,
):
    shard_factory()
    response = client.get(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh-status",
        headers=authorization_header,
    )
    assert response.json()["status"] == "Unknown"
    assert response.json()["details"] is None


def test_refresh_shard_status_is_failed(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    dataset_refresh_is_failed,
):
    shard_factory()
    response = client.get(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh-status",
        headers=authorization_header,
    )
    assert response.json()["status"] == "Failed"
    assert response.json()["details"] == {
        "errorCode": "ModelRefresh_ShortMessage_ProcessingError",
        "errorDescription": "Resource Governing: This operation was canceled because ...",
    }


def test_trigger_incremental_refresh(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    incremental_refresh_workspace_dataset,
):
    shard_factory()
    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh/incremental",
        headers=authorization_header,
    )
    response.raise_for_status()


def test_trigger_full_refresh(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    refresh_workspace_dataset,
):
    shard_factory()
    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh/full",
        headers=authorization_header,
    )
    response.raise_for_status()


def test_workspace_is_already_being_refreshed(
    db_session: Session,
    client,
    authorization_header,
    shard_factory,
    workspace_dataset_is_already_being_refreshed,
):
    shard_factory()
    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh/full",
        headers=authorization_header,
    )
    response.raise_for_status()

    assert response.status_code == HTTP_202_ACCEPTED
    assert response.json() == {"detail": "Another refresh request is already executing"}


def test_trigger_full_refresh_is_ignored_when_ignore_header_passed(
    client,
    authorization_header,
    shard_factory,
):
    shard_factory()
    headers = {"ignore": "yes", **authorization_header}

    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/refresh/full",
        headers=headers,
    )
    response.raise_for_status()


def test_get_shard_profiles_when_no_related_profiles_should_return_empty(
    client, authorization_header, shard_factory
):
    shard = shard_factory()

    response = client.get(
        f"/shard/{shard.workspace_id}/profiles",
        headers=authorization_header,
    )
    response.raise_for_status()

    assert response.status_code == HTTP_200_OK
    assert response.json() == []


def test_get_shard_profiles_when_related_profiles_exist_should_return_those_profiles(
    client, authorization_header, shard_factory, profile_factory
):
    shard = shard_factory()
    profile1 = profile_factory(permission_set_uuid=shard.permission_set_uuid)
    profile2 = profile_factory(permission_set_uuid=shard.permission_set_uuid)

    response = client.get(
        f"/shard/{shard.workspace_id}/profiles",
        headers=authorization_header,
    )
    response.raise_for_status()

    assert response.status_code == HTTP_200_OK
    assert {profile["profile_id"] for profile in response.json()} == {
        profile1.profile_id,
        profile2.profile_id,
    }


def test_base64_query_endpoint_returns_requested_sku_data(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_sku_query_mock,
):
    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/query",
        headers=authorization_header,
        json={
            "base64_query": "CiAgICAgICAgICAgIEVWQUxVQVRFIFNFTEVDVENPTFVNTlMoZGltX3NrdSwKICAgICAgICAgICAgInVuaXF1ZV9za3VfaWQiLCBbdW5pcXVlX3NrdV9pZF0sCiAgICAgICAgICAgICJiYXNlX3NrdV9pZCIsIFtiYXNlX3NrdV9pZF0sCiAgICAgICAgICAgICJwb3J0YWxfcGxhdGZvcm1fcmVnaW9uIiwgW3BvcnRhbF9wbGF0Zm9ybV9yZWdpb25dLAogICAgICAgICAgICAiZ3NvIiwgW2dzb10sCiAgICAgICAgICAgICJodW1hbl9uYW1lIiwgW2h1bWFuX25hbWVdLAogICAgICAgICAgICAicHJvZHVjdF9uYW1lIiwgUkVMQVRFRChkaW1fcHJvZHVjdHNbcHJvZHVjdF9uYW1lXSksCiAgICAgICAgICAgICJza3VfdHlwZSIsIFtza3VfdHlwZV0sCiAgICAgICAgICAgICJzdHVkaW9faWQiLCBSRUxBVEVEKGRpbV9wcm9kdWN0c1tzdHVkaW9faWRdKSwKICAgICAgICAgICAgInByb2R1Y3RfaWQiLCBbcHJvZHVjdF9pZF0sCiAgICAgICAgICAgICJyZWxlYXNlX2RhdGUiLCBbcmVsZWFzZV9kYXRlXSkKICAgICAgICAgICAg"
        },
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "[unique_sku_id]": "42ccb715787e47f48648159060135ef1-epic:2",
            "[base_sku_id]": "42ccb715787e47f48648159060135ef1",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 11,
            "[human_name]": "SUPERHOT",
            "[product_name]": "SUPERHOT",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
        {
            "[unique_sku_id]": "942e9424124145fca7fadefefb9b70e6-epic:2",
            "[base_sku_id]": "942e9424124145fca7fadefefb9b70e6",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 12,
            "[human_name]": "SUPERHOT: Mind Control Delete",
            "[product_name]": "SUPERHOT MCD",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT MCD:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
    ]


def test_raw_query_endpoint_returns_requested_sku_data(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_sku_query_mock,
):
    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/query",
        headers=authorization_header,
        json={
            "raw_query": """
            EVALUATE SELECTCOLUMNS(dim_sku,
            "unique_sku_id", [unique_sku_id],
            "base_sku_id", [base_sku_id],
            "portal_platform_region", [portal_platform_region],
            "gso", [gso],
            "human_name", [human_name],
            "product_name", RELATED(dim_products[product_name]),
            "sku_type", [sku_type],
            "studio_id", RELATED(dim_products[studio_id]),
            "product_id", [product_id],
            "release_date", [release_date])
            """
        },
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "[unique_sku_id]": "42ccb715787e47f48648159060135ef1-epic:2",
            "[base_sku_id]": "42ccb715787e47f48648159060135ef1",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 11,
            "[human_name]": "SUPERHOT",
            "[product_name]": "SUPERHOT",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
        {
            "[unique_sku_id]": "942e9424124145fca7fadefefb9b70e6-epic:2",
            "[base_sku_id]": "942e9424124145fca7fadefefb9b70e6",
            "[portal_platform_region]": "Epic:PC:Global",
            "[gso]": 12,
            "[human_name]": "SUPERHOT: Mind Control Delete",
            "[product_name]": "SUPERHOT MCD",
            "[sku_type]": "SALES",
            "[studio_id]": 2,
            "[product_id]": "SUPERHOT MCD:101010:2",
            "[release_date]": "2014-06-14T00:00:00",
        },
    ]


def test_query_endpoint_returns_requested_portal_data(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    dim_portals_query_mock,
):
    response = client.post(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/query",
        headers=authorization_header,
        json={
            "base64_query": "CiAgICAgICAgICAgIEVWQUxVQVRFIFNFTEVDVENPTFVNTlMoZGltX3BvcnRhbHMsCiAgICAgICAgICAgICJwb3J0YWxfcGxhdGZvcm1fcmVnaW9uIiwgW3BvcnRhbF9wbGF0Zm9ybV9yZWdpb25dLAogICAgICAgICAgICAicG9ydGFsIiwgW3BvcnRhbF0sCiAgICAgICAgICAgICJwbGF0Zm9ybSIsIFtwbGF0Zm9ybV0sCiAgICAgICAgICAgICJyZWdpb24iLCBbcmVnaW9uXSwKICAgICAgICAgICAgInN0b3JlIiwgW3N0b3JlXSwKICAgICAgICAgICAgImFiYnJldmlhdGVkX25hbWUiLCBbYWJicmV2aWF0ZWRfbmFtZV0KICAgICAgICAgICAgKQ=="
        },
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "[portal_platform_region]": "Steam:PC:Global",
            "[portal]": "Steam",
            "[platform]": "PC",
            "[region]": "Global",
            "[store]": "Steam",
            "[abbreviated_name]": "Steam",
        },
        {
            "[portal_platform_region]": "Meta:Rift:Global",
            "[portal]": "Meta",
            "[platform]": "Rift",
            "[region]": "Global",
            "[store]": "Meta Rift",
            "[abbreviated_name]": "Rift",
        },
        {
            "[portal_platform_region]": "Meta:Quest:Global",
            "[portal]": "Meta",
            "[platform]": "Quest",
            "[region]": "Global",
            "[store]": "Meta Quest",
            "[abbreviated_name]": "Quest",
        },
    ]


def test_permission_set_endpoint_returns_permission_set(
    client,
    authorization_header,
    existing_customer,
):
    response = client.get(
        "/shard/*************-40f1-bf7d-13880a5c3ce5/permission-set",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK
    assert response.json() == [{"studio_id": 1, "product_name": None}]


def test_permission_set_endpoint_returns_404(
    client,
    authorization_header,
    existing_customer,
):
    response = client.get(
        "/shard/bad22222-1758-40f1-bf7d-13880a5c3ce5/permission-set",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}
