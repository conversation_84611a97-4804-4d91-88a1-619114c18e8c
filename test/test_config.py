from dataset_manager.config import PBICredentials, Settings


def test_pbi_credentials_from_env(monkeypatch):
    monkeypatch.setenv("PBI_AZURE_TENANT_ID", "test-tenant-id")

    monkeypatch.setenv("PBI_CREDENTIALS__0__CLIENT_ID", "test-client-id-1")
    monkeypatch.setenv("PBI_CREDENTIALS__0__CLIENT_SECRET", "test-secret-1")
    monkeypatch.setenv("PBI_CREDENTIALS__0__OBJECT_ID", "test-object-id-1")

    monkeypatch.setenv("PBI_CREDENTIALS__1__CLIENT_ID", "test-client-id-2")
    monkeypatch.setenv("PBI_CREDENTIALS__1__CLIENT_SECRET", "test-secret-2")
    monkeypatch.setenv("PBI_CREDENTIALS__1__OBJECT_ID", "test-object-id-2")

    settings = Settings()

    assert settings.pbi_credentials == [
        PBICredentials(
            index=0,
            client_id="test-client-id-1",
            client_secret="test-secret-1",
            object_id="test-object-id-1",
        ),
        PBICredentials(
            index=1,
            client_id="test-client-id-2",
            client_secret="test-secret-2",
            object_id="test-object-id-2",
        ),
    ]
    assert settings.pbi_azure_tenant_id == "test-tenant-id"
