<!--- docs
# Metadata used by our doc generator
title: Dataset Manager
group: services
-->

# Dataset Manager
Services that manages PowerBI (PBI) workspaces.

## Develop application locally

1. Configure and install requirements:
    ```bash
    poetry env use 3.10
    poetry install
    ```

1. Run required services (database, migrations and azurite)
    ```bash
    az acr login --name crindie<PERSON><PERSON><PERSON>
    docker-compose up -d
    ```

1. Start `uvicorn` server:
    ```bash
    poetry run uvicorn dataset_manager.main:app --reload
    ```
    or in VSCode `Run and Debug` tab select `Dataset Manager: API` and press `Start Debugging`

## Testing
0. Connect to VPN
1. Start docker compose:
    ```bash
    az login
    az acr login --name crindie<PERSON><PERSON><PERSON>
    docker-compose up -d
    ```

2. *(MacOS only)* Install ODBC driver with Homebrew:

    When running the application, you may encounter the following error:
    ```
    > import pyodbc
   
    ImportError: dlopen(/Users/<USER>/Projects/dataset-manager/.venv/lib/python3.10/site-packages/pyodbc.cpython-310-darwin.so, 0x0002): Library not loaded: /opt/homebrew/opt/unixodbc/lib/libodbc.2.dylib
     Referenced from: <781089AF-D675-3AD0-BEC3-B9D75C17B5AF> /Users/<USER>/Projects/dataset-manager/.venv/lib/python3.10/site-packages/pyodbc.cpython-310-darwin.so
     Reason: tried: '/opt/homebrew/opt/unixodbc/lib/libodbc.2.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/opt/homebrew/opt/unixodbc/lib/libodbc.2.dylib' (no such file), '/opt/homebrew/opt/unixodbc/lib/libodbc.2.dylib' (no such file)
    ```
    
    To fix the error, you need to install `unixodbc` using Homebrew:

    ```bash
    brew install unixodbc
    ```

3. Run all tests:

    ```bash
    poetry run pytest
    ```

## Azurite
For testing blob we are using [Azurite](https://github.com/Azure/Azurite) emulator. It is started by `docker-compose`. If you want to explore content of blobs you can download [Azure Storage Explorer](https://azure.microsoft.com/en-gb/products/storage/storage-explorer/)
## Alembic migrations

### Create a new migration  
```bash
$ poetry run alembic --config alembic.ini revision -m "<migration-name>"
```
New migration file is created in `alembic/versions/`
### Run migration manually
(automatically migrations are executed by `docker-compose`)
```bash
$ poetry run alembic --config alembic.ini upgrade head
```
By default migrations will be run on localhost. To run them on different environments set `SQLALCHEMY_DATABASE_URL`.  
Remember to escape special characters i.e. `!` -> `\!`  
For example, to run migration on db named indiebi running in docker-compose:
```bash
$ SQLALCHEMY_DATABASE_URL=mssql+pyodbc://indiebi:Password1\!\@sql-server/Reports?driver=ODBC+Driver+17+for+SQL+Server
$ poetry run alembic --config alembic.ini upgrade head
$ unset SQLALCHEMY_DATABASE_URL
```
### Revert migration:
```bash
$ poetry run alembic --config alembic.ini 
```

## Cleaning abandoned profiles
We should clean profiles automatically. For now we have a script that can be run manually:
```bash
$ poetry run python scripts/delete_abandoned_profiles.py
```
It fetches all profiles from DM and all users from User Service. Then it compares the two lists and deletes all profiles that are not in User Service.

## Provisioning users and schemas in Azure SQL
If you're deploying on Azure, after provisioning new database or making changes in SQL users/schemas,
you need to run as DB admin (after login with `az login`):
```
$ poetry run scripts/setup_db.py db-host k8s-pod-identity-name gitlab-sp-name"
```
This script needs to be run manue because GitLab CI Service Principal doesn't have
permissions to create DB users based on AD users/groups.

Complete command is always outputted and the of terraform stage in CI pipeline.

