{"editor.formatOnSave": true, "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}}, "python.testing.pytestArgs": ["-vvv"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.analysis.typeCheckingMode": "basic", "cSpell.words": ["adls", "outerjoin", "pbix", "powerbi", "sqltypes"]}