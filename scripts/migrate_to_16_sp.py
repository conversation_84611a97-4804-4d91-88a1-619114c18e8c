"""
This script is used to grant access to designated Service Principals for specific shards
based on their permission set UUIDs.

To use this script:
1. Create a .env file using .env_example as a template
2. Copy and paste values from ArgoCD -> dataset-manager -> dataset-manager-config-map
3. Set PBI_VISUALS_VERSION to the latest version you want to assign

The script needs to be run locally for each environment separately. After execution,
you can deploy the new version of dataset manager that works with these 16 Service
Principals.

This approach allows us to perform the migration without executing a full release,
avoiding long waiting times typically associated with the release process.

"""

import httpx
from azure.identity import ClientSecretCredential
from pydantic_settings import BaseSettings, SettingsConfigDict
from tqdm import tqdm

from dataset_manager.azure.identity import CredentialsContext
from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI, get_workspace_api

raw_values_from_argocd = """
  !!! example values !!!
  PBI_CREDENTIALS__0__CLIENT_ID: 12345678-1234-5678-1234-567812345678
  PBI_CREDENTIALS__0__OBJECT_ID: abcdef12-3456-7890-abcd-ef1234567890
  PBI_CREDENTIALS__2__CLIENT_ID: 98765432-9876-5432-9876-543298765432
  PBI_CREDENTIALS__2__OBJECT_ID: fedcba98-7654-3210-fedc-ba9876543210
  .... etc ...
  """

PBI_VISUALS_VERSION = "3.10.0"

OBJECT_IDS: dict[int, str] = {
    int(line.split("__")[1]): line.split(": ")[1].strip()
    for line in raw_values_from_argocd.splitlines()
    if "OBJECT_ID" in line
}


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )
    env: str = "local"
    user_agent: str = "migrate_to_16_sp.py"

    dataset_manager_url: str = "https://dataset-manager.indiebi.dev/"
    dataset_manager_key: str

    tenant_id: str
    client_id: str
    client_secret: str


def migrate_to_16_sp(
    env: str, dataset_manager_client: httpx.Client, workspace_api: WorkspaceAPI
):
    all_shards = dataset_manager_client.get("/shard/active").json()

    for shard in tqdm(all_shards):
        credentials_context = CredentialsContext(shard["permission_set_uuid"])
        print(f"Migrating {shard['workspace_name']}", credentials_context.index)

        workspace_api.assign_admins(
            shard["workspace_id"],
            admins=[
                {
                    "groupUserAccessRight": "Admin",
                    "displayName": f"Service Principal {env} {credentials_context.index}",
                    "identifier": OBJECT_IDS[credentials_context.index],
                    "principalType": "App",
                    "emailAddress": OBJECT_IDS[credentials_context.index],
                }
            ],
        )

    versions = dataset_manager_client.get(f"version/{PBI_VISUALS_VERSION}").json()
    for workspace_id in [
        versions["shard_template_workspace_id"],
        versions["visuals_workspace_id"],
    ]:
        print("Removing deprecated profiles from ", workspace_id)
        users = workspace_api._get_users(workspace_id=workspace_id)
        for user in tqdm(users):
            if user.get("profile") is None:
                continue
            workspace_api._delete_profile(
                workspace_id=workspace_id,
                identifier=user["identifier"],
                profile_id=user["profile"]["id"],
            )

    for index, object_id in OBJECT_IDS.items():
        for workspace_id in [
            versions["shard_template_workspace_id"],
            versions["visuals_workspace_id"],
        ]:
            print(f"Adding {object_id} to version workspace {workspace_id}")
            workspace_api.assign_admins(
                workspace_id,
                admins=[
                    {
                        "groupUserAccessRight": "Admin",
                        "displayName": f"Service Principal {env} {index}",
                        "identifier": object_id,
                        "principalType": "App",
                        "emailAddress": object_id,
                    }
                ],
            )


def main():
    settings = Settings()

    dataset_manager_client = httpx.Client(
        base_url=settings.dataset_manager_url,
        headers={"x-api-key": settings.dataset_manager_key},
    )

    credentials = ClientSecretCredential(
        tenant_id=settings.tenant_id,
        client_id=settings.client_id,
        client_secret=settings.client_secret,
    )

    def auth_token_provider() -> str:
        my_token = credentials.get_token(
            "https://analysis.windows.net/powerbi/api/.default"
        ).token
        return f"Bearer {my_token}"

    workspace_api = get_workspace_api(auth_token_provider=auth_token_provider)

    migrate_to_16_sp(settings.env, dataset_manager_client, workspace_api)


if __name__ == "__main__":
    main()
