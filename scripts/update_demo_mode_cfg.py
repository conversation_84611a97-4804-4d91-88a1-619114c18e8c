import json
from datetime import datetime
from functools import partial
from pathlib import Path

import fire

from dataset_manager.azure.client import auth_token_provider
from dataset_manager.azure.powerbi.datasets import get_dataset_api
from dataset_manager.azure.powerbi.reports import get_reports_api
from dataset_manager.config import get_modern_settings
from dataset_manager.entities import WorkspaceId
from dataset_manager.logic.version import DesktopDefinitionTemplate


def update_config_to(workspace_id: str):
    config = get_modern_settings()

    management_auth_token_provider = partial(auth_token_provider, config.pbi_token_url)

    reports_api = get_reports_api(auth_token_provider=management_auth_token_provider)
    dataset_api = get_dataset_api(auth_token_provider=management_auth_token_provider)

    dataset_id = dataset_api.get_dataset(
        workspace_id=WorkspaceId(workspace_id),
        dataset_name="Dataset",
    )

    print("Dataset id:", dataset_id)

    reports = reports_api.list(workspace_id)

    visuals_desktop_definitions = DesktopDefinitionTemplate(
        template_path=Path("static/desktop-definitions-template.json"),
        workspace_id=workspace_id,
        reports=reports,
    ).fill()

    visuals_desktop_definitions["version"] = config.demo_mode.version
    visuals_desktop_definitions["last_updated"] = datetime.now().isoformat()

    report_ids = [report["id"] for report in reports]
    print("Reports ids:", report_ids)

    with config.demo_mode.desktop_definitions.open("w") as f:
        f.write(json.dumps(visuals_desktop_definitions, indent=4))


if __name__ == "__main__":
    fire.Fire(update_config_to)
