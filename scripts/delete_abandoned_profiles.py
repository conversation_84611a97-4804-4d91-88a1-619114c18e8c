import fire
import httpx
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )
    user_agent: str = "delete_abandoned_profiles.py"

    user_service_url: str = "https://user-service-v2.indiebi.dev/"
    user_service_key: str

    dataset_manager_url: str = "https://dataset-manager.indiebi.dev/"
    dataset_manager_key: str


class UserServiceClient:
    def __init__(self, settings: Settings) -> None:
        self._client = httpx.Client(
            base_url=settings.user_service_url,
            headers={
                "x-api-key": settings.user_service_key,
                "User-Agent": settings.user_agent,
            },
        )

    def _get_paged(
        self, url: str, *, limit: int = 1000, offset: int = 0, **kwargs
    ) -> list[dict]:
        params = kwargs.get("params", {})
        params.update({"limit": limit, "offset": offset})
        kwargs["params"] = params
        data = []
        while True:
            response = self._client.get(url=url, **kwargs)  # noqa: S113
            response_data = response.json()
            data.extend(response_data["data"])

            if len(data) >= response_data["count"] or response_data["count"] == 0:
                break

            kwargs["params"]["offset"] += limit

        return data

    def get_all_users(self):
        return self._get_paged("/user/search", timeout=90)


def delete_abandoned_profiles():
    settings = Settings()

    dataset_manager_client = httpx.Client(
        base_url=settings.dataset_manager_url,
        headers={"x-api-key": settings.dataset_manager_key},
    )

    all_studios_in_dm = [
        profile["studio_id"]
        for profile in dataset_manager_client.get("/profile/all").json()
    ]

    user_service_studios = [
        user["legacy_id"] for user in UserServiceClient(settings).get_all_users()
    ]
    abandoned_studios = set(all_studios_in_dm) - set(user_service_studios)
    for studio_id in abandoned_studios:
        print(f"Deleting studio {studio_id}")
        dataset_manager_client.delete(f"/studio/{studio_id}")


if __name__ == "__main__":
    fire.Fire(delete_abandoned_profiles)
