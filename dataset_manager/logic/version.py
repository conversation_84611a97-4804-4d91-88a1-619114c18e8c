import json
import logging
from datetime import datetime
from pathlib import Path
from time import sleep

from pydantic import BaseModel, ValidationError, model_validator
from pydantic_core import InitErrorDetails
from sqlalchemy.orm import Session

from dataset_manager.azure.blob import <PERSON><PERSON>ontainer
from dataset_manager.azure.powerbi.imports import ImportsAPI
from dataset_manager.azure.powerbi.reports import ReportsAPI
from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI
from dataset_manager.config import PBICredentials
from dataset_manager.entities import CapacityId, WorkspaceName
from dataset_manager.repo import Repo
from dataset_manager.repo.release import CreateRelease
from dataset_manager.repo.version import (
    DatasetTemplateWorkspaceId,
    ShardVersion,
    Version,
    VersionExtended,
    VersionId,
    VersionRepo,
    VisualsDatasetId,
    VisualsWorkspaceId,
)

log = logging.getLogger(__name__)


class FrozenVersionAlreadyCreated(Exception):
    pass


class PBIXFile(BaseModel):
    name: str
    content: bytes


class NewVersionRequest(BaseModel):
    version_id: VersionId
    shard_version: ShardVersion
    dataset: PBIXFile | None
    visuals: list[PBIXFile]
    workspaces_prefix: str
    desktop_definitions_template_path: Path
    workspace_admins_credentials: list[PBICredentials]

    @property
    def workspace_admins(self) -> list[dict]:
        return [
            {
                "groupUserAccessRight": "Admin",
                "displayName": credential.display_name,
                "identifier": credential.object_id,
                "principalType": "App",
                "emailAddress": credential.object_id,
            }
            for credential in self.workspace_admins_credentials
        ]

    @property
    def deploy_dataset(self) -> bool:
        return self.version_id == self.shard_version

    @property
    def dataset_workspace_name(self) -> WorkspaceName:
        return WorkspaceName(f"{self.workspaces_prefix}_{self.version_id}_template")

    @property
    def visuals_workspace_name(self) -> WorkspaceName:
        return WorkspaceName(f"{self.workspaces_prefix}_{self.version_id}_visuals")

    @model_validator(mode="before")
    def validate_version(cls, values):
        if (
            values["version_id"] == values["shard_version"]
            and values["dataset"] is None
        ):
            raise ValidationError.from_exception_data(
                title="Field is required when creating version with new model",
                line_errors=[
                    InitErrorDetails(
                        type="missing",
                        loc=("__root__", "dataset"),
                        input=None,
                        hide_input=True,
                    )
                ],
            )
        return values


class NewVersionResponse(BaseModel):
    request: NewVersionRequest
    version: Version


class DesktopDefinitionTemplate:
    def __init__(self, template_path: Path, workspace_id: str, reports) -> None:
        self._template_path = template_path
        self._workspace_id = workspace_id
        self._reports = reports

    def fill(self) -> dict:
        with self._template_path.open("r") as json_file:
            content = json.load(json_file)

        report_urls = {
            r["name"]: self._generate_embed_url(report_id=r["id"])
            for r in self._reports
        }
        for menu_item in content["menu_items"]:
            if "dashboard_url" in menu_item:
                if menu_item["pbix_name"] not in report_urls:
                    continue
                menu_item["dashboard_url"] = report_urls[menu_item["pbix_name"]]
            elif "children" in menu_item:
                for child in menu_item["children"]:
                    if child["pbix_name"] not in report_urls:
                        continue
                    if "dashboard_url" in child:
                        child["dashboard_url"] = report_urls[child["pbix_name"]]

        return content

    def _generate_embed_url(self, report_id: str) -> str:
        # This static config speeds up dashboard loading time and is fixed for the whole region
        config_part = "config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly93YWJpLXdlc3QtZXVyb3BlLWUtcHJpbWFyeS1yZWRpcmVjdC5hbmFseXNpcy53aW5kb3dzLm5ldCIsImVtYmVkRmVhdHVyZXMiOnsidXNhZ2VNZXRyaWNzVk5leHQiOnRydWV9LCJhbGciOiJIUzI1NiJ9.e30.DniugsEtOvpzMG9XosDAjacUkHQY0s4_vwSG6QqUR3c"
        return f"https://app.powerbi.com/reportEmbed?reportId={report_id}&groupId={self._workspace_id}&language=en&formatLocale=en-US&{config_part}"


class NewVersionUseCase:
    def __init__(
        self,
        repo: Repo,
        reports_api: ReportsAPI,
        imports_api: ImportsAPI,
        workspace_api: WorkspaceAPI,
        model_container: ModelContainer,
        request: NewVersionRequest,
    ) -> None:
        self._repo: Repo = repo
        self._request = request
        self._reports_api = reports_api
        self._imports_api = imports_api
        self._workspace_api: WorkspaceAPI = workspace_api
        self._model_container: ModelContainer = model_container

    def process(self) -> NewVersionResponse:
        if self._request.deploy_dataset:
            dataset_workspace_id, dataset_id = self._deploy_dataset()
        else:
            dataset_workspace_id, dataset_id = self._get_existing_dataset()

        visuals_workspace_id, reports = self._deploy_visuals(dataset_id)

        visuals_desktop_definitions = self._create_visuals_desktop_definitions(
            visuals_workspace_id,
            reports,
        )

        version = self._repo.version.save(
            VersionExtended(
                id=self._request.version_id,
                shard_version=self._request.shard_version,
                shard_template_workspace_id=DatasetTemplateWorkspaceId(
                    dataset_workspace_id
                ),
                visuals_workspace_id=VisualsWorkspaceId(visuals_workspace_id),
                visuals_dataset_id=VisualsDatasetId(dataset_id),
                visuals_reports_ids=[r["id"] for r in reports],
                visuals_desktop_definitions=visuals_desktop_definitions,
                is_active=True,
                is_default=False,
            )
        )

        for release in self._release_for():
            self._repo.release.create(release)

        return NewVersionResponse(request=self._request, version=version)

    def _deploy_dataset(self):
        dataset_workspace_id = self._workspace_api.create(
            workspace_name=self._request.dataset_workspace_name
        )
        self._workspace_api.assign_to_capacity(
            workspace_id=dataset_workspace_id,
            capacity_id=self._default_capacity_id,
        )
        log.info("Assigning service principals to %s", dataset_workspace_id)
        self._workspace_api.assign_admins(
            workspace_id=dataset_workspace_id,
            admins=self._request.workspace_admins,
        )

        assert self._request.dataset is not None
        dataset_id = self._imports_api.deploy_dataset(
            workspace_id=dataset_workspace_id,
            dataset_pbix=self._request.dataset.content,
        )

        self._model_container.upload(
            name=self._request.shard_version, data=self._request.dataset.content
        )

        return dataset_workspace_id, dataset_id

    def _get_existing_dataset(self):
        version_for_existing_shard_version = self._repo.version.get_for_shard_version(
            shard_version=self._request.shard_version
        )
        dataset_workspace_id = (
            version_for_existing_shard_version.shard_template_workspace_id
        )
        dataset_id = version_for_existing_shard_version.visuals_dataset_id
        return dataset_workspace_id, dataset_id

    def _deploy_visuals(self, dataset_id):
        visuals_workspace_id = self._workspace_api.create(
            workspace_name=self._request.visuals_workspace_name,
        )
        self._workspace_api.assign_to_capacity(
            workspace_id=visuals_workspace_id,
            capacity_id=self._default_capacity_id,
        )
        log.info("Assigning service principals to %s", visuals_workspace_id)
        self._workspace_api.assign_admins(
            workspace_id=visuals_workspace_id,
            admins=self._request.workspace_admins,
        )

        for visual in self._request.visuals:
            self._imports_api.deploy_visual(
                workspace_id=visuals_workspace_id,
                display_name=visual.name.removesuffix(".pbix"),
                dataset_id=dataset_id,
                visual_pbix=visual.content,
            )

        reports = self._reports_api.list(visuals_workspace_id)
        while len(reports) < len(self._request.visuals):
            sleep(1)
            reports = self._reports_api.list(visuals_workspace_id)

        return visuals_workspace_id, reports

    def _create_visuals_desktop_definitions(
        self,
        visuals_workspace_id,
        reports,
    ) -> dict:
        content = DesktopDefinitionTemplate(
            template_path=self._request.desktop_definitions_template_path,
            workspace_id=visuals_workspace_id,
            reports=reports,
        ).fill()

        content["version"] = self._request.version_id
        content["last_updated"] = datetime.now().isoformat()

        return content

    def _release_for(self) -> list[CreateRelease]:
        result = []
        if (
            self._request.deploy_dataset
            and self._request.version_id.full_recreate_on_release
        ):
            for profile in self._repo.profile.get_for_active_version(
                active_version_id=self._request.version_id
            ):
                assert profile.permission_set_uuid is not None
                result.append(
                    CreateRelease(
                        studio_id=profile.studio_id,
                        version_id=self._request.version_id,
                        permission_set_uuid=profile.permission_set_uuid,
                        is_full_recreate=self._request.version_id.full_recreate_on_release,
                    )
                )
        return sorted(result, key=lambda r: (r.studio_id, r.permission_set_uuid))

    @property
    def _default_capacity_id(self) -> CapacityId:
        return self._repo.capacity.default().id


def set_default_version(session: Session, version_id: VersionId) -> Version:
    VersionRepo(session=session).unset_default_for_all()
    version = VersionRepo(session=session).get(version_id=version_id)
    version.is_default = True
    return VersionRepo(session=session).save(version)
