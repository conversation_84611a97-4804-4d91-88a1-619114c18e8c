import logging

from dataset_manager.azure.powerbi.profiles import ProfileAPI
from dataset_manager.entities import PermissionSetUUID
from dataset_manager.repo.profile import Profile, ProfileProperties, ProfileRepo
from dataset_manager.repo.version import VersionId

log = logging.getLogger(__name__)


def create_profile(
    profile_api: ProfileAPI,
    profile_repo: ProfileRepo,
    environment: str,
    studio_id: int,
    active_version_id: VersionId,
    permission_set_uuid: PermissionSetUUID,
) -> Profile:
    profile_name = f"{environment}_{studio_id}"
    profile_id = profile_api.create(profile_name)

    return profile_repo.create(
        new_profile=ProfileProperties(
            studio_id=studio_id,
            profile_id=profile_id,
            profile_name=profile_name,
            active_version_id=active_version_id,
            permission_set_uuid=permission_set_uuid,
        )
    )
