import logging

from fastapi import APIRouter
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    RepoDependency,
    ScopedAioDatasetAPIDependency,
    ScopedEmbedAPIDependency,
    SessionContextDependency,
    StudioIdDependency,
    UnboundShardDependency,
    get_scoped_dataset_api,
)
from dataset_manager.config import SettingsDependency
from dataset_manager.entities import (
    SKU,
    BaseStrField,
    DaxQuery,
    Portal,
    Region,
    Slicer,
    Studio,
)
from dataset_manager.logic.shard import fill_last_refresh_timestamp
from dataset_manager.repo.permission_set import (
    CreatePermissionSet,
    PermissionSet,
    PermissionSetRepo,
)
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.release import CreateRelease
from dataset_manager.repo.shard import Shard, ShardRepo
from dataset_manager.repo.version import VersionId, VersionRepo

log = logging.getLogger(__name__)


shared_router = APIRouter(tags=[ApiTag.PUBLIC])
public_router = APIRouter(tags=[ApiTag.PUBLIC])
private_router = APIRouter(tags=[ApiTag.PRIVATE])


class SecurityKey(BaseStrField):
    pass


class EmbedToken(BaseModel):
    token: str
    expiration_date: str
    dataset_id: str
    version: VersionId
    security_key_value: SecurityKey
    hidden_slicers: bool


@public_router.get(
    "/dashboards-auth-token",
    response_model=EmbedToken,
)
@public_router.get(
    "/dashboards-auth-token/version/{requested_version}",
    response_model=EmbedToken,
)
def get_dashboards_auth_token(
    embed_api: ScopedEmbedAPIDependency,
    studio_id: StudioIdDependency,
    session_context: SessionContextDependency,
    requested_version: VersionId | None = None,
    lifetime_in_minutes: int = 0,
):
    with session_context() as session:
        profile = ProfileRepo(session=session).get_for_studio(studio_id=studio_id)

        version = VersionRepo(session=session).get(
            requested_version or profile.active_version_id
        )
        assert profile.permission_set_uuid is not None
        permission_set = PermissionSetRepo(session=session).get(
            uuid=profile.permission_set_uuid
        )
        shard = ShardRepo(session=session).get_for_permission_set_and_version(
            permission_set=permission_set,
            version=version,
        )

    token, expiration = embed_api.generate_token(
        dataset_ids=[shard.dataset_id, version.visuals_dataset_id],
        reports_ids=version.visuals_reports_ids,
        lifetime_in_minutes=lifetime_in_minutes,
    )

    return EmbedToken(
        token=token,
        expiration_date=expiration,
        dataset_id=shard.dataset_id,
        version=version.id,
        security_key_value=SecurityKey(permission_set.permission_set_json),
        hidden_slicers=True,  # TODO: ask SaaS team if we can remove it
    )


@public_router.get("/desktop-definitions")
@public_router.get("/desktop-definitions/version/{requested_version}")
def get_desktop_definitions(
    repo: RepoDependency,
    studio_id: StudioIdDependency,
    requested_version: VersionId | None = None,
):
    profile = repo.profile.get_for_studio(studio_id=studio_id)
    version = repo.version.get_extended(requested_version or profile.active_version_id)

    return JSONResponse(content=jsonable_encoder(version.visuals_desktop_definitions))


@shared_router.get("/last-refresh")
def get_last_refresh_date(
    settings: SettingsDependency,
    shard: UnboundShardDependency,
    session_context: SessionContextDependency,
):
    if shard.last_refresh_timestamp is None:
        dataset_api = get_scoped_dataset_api(shard, settings)
        with session_context() as session:
            shard = fill_last_refresh_timestamp(
                session=session, dataset_api=dataset_api, shard=shard
            )
    return shard.last_refresh_timestamp


def _convert_sku_from_dax(table_row: dict) -> SKU:
    return SKU(
        unique_sku_id=table_row["[unique_sku_id]"],
        base_sku_id=table_row["[base_sku_id]"],
        portal_platform_region=table_row["[portal_platform_region]"],
        gso=table_row["[gso]"],
        name=table_row["[human_name]"],
        product_name=table_row["[product_name]"],
        sku_type=table_row["[sku_type]"],
        studio_id=table_row["[studio_id]"],
        product_id=table_row["[product_id]"],
        release_date=table_row["[release_date]"],
    )


def _convert_portal_from_dax(table_row: dict) -> Portal:
    return Portal(
        portal_platform_region=table_row["[portal_platform_region]"],
        portal=table_row["[portal]"],
        platform=table_row["[platform]"],
        region=table_row["[region]"],
        store=table_row["[store]"],
        abbreviated_name=table_row["[abbreviated_name]"],
    )


def _convert_studio_from_dax(table_row: dict) -> Studio:
    return Studio(
        company_name=table_row["dim_studio[company_name]"],
        studio_id=table_row["dim_studio[studio_id]"],
    )


def _convert_region_from_dax(table_row: dict) -> Region:
    return Region(
        region=table_row["dim_country[region]"],
        country_name=table_row["dim_country[country_name]"],
    )


@shared_router.get("/sku")
async def get_skus(
    shard: UnboundShardDependency,
    dataset_api: ScopedAioDatasetAPIDependency,
) -> list[SKU]:
    query = """
            EVALUATE SELECTCOLUMNS(dim_sku,
            "unique_sku_id", [unique_sku_id],
            "base_sku_id", [base_sku_id],
            "portal_platform_region", [portal_platform_region],
            "gso", [gso],
            "human_name", [human_name],
            "product_name", RELATED(dim_products[product_name]),
            "sku_type", [sku_type],
            "studio_id", RELATED(dim_products[studio_id]),
            "product_id", [product_id],
            "release_date", [release_date])
            """
    raw_result = await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        query,
    )
    return list(map(_convert_sku_from_dax, raw_result))


@shared_router.get("/portal")
async def get_portals(
    shard: UnboundShardDependency,
    dataset_api: ScopedAioDatasetAPIDependency,
) -> list[Portal]:
    query = """
            EVALUATE SELECTCOLUMNS(dim_portals,
            "portal_platform_region", [portal_platform_region],
            "portal", [portal],
            "platform", [platform],
            "region", [region],
            "store", [store],
            "abbreviated_name", [abbreviated_name]
            )"""
    raw_response = await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        query,
    )
    return list(map(_convert_portal_from_dax, raw_response))


revenue_measures = [
    {
        "label": "Gross Revenue",
        "value": "'Core Measures'[Gross Sales Total]",
        "type": "revenue",
    },
    {
        "label": "Refunded revenue",
        "value": "'Core Measures'[Gross Returned]",
        "type": "revenue",
    },
    {
        "label": "Revenue (excl. refunds)",
        "value": "'Core Measures'[Gross Sales]",
        "type": "revenue",
    },
    {
        "label": "Approx. net revenue",
        "value": "'Core Measures'[Approx Net Revenue]",
        "type": "revenue",
    },
]

units_measures = [
    {
        "label": "All units",
        "value": "'Core Measures'[Units Total]",
        "type": "units",
    },
    {
        "label": "Units (excl. returns)",
        "value": "'Core Measures'[Units]",
        "type": "units",
    },
    {
        "label": "Refunded units",
        "value": "'Core Measures'[Units Returned]",
        "type": "units",
    },
    {
        "label": "Free units",
        "value": "'Core Measures'[Units Freely Distributed]",
        "type": "units",
    },
    {
        "label": "Activated retail units",
        "value": "'Core Measures'[Units Sold in Retail]",
        "type": "units",
    },
]

units_measures_excl_returns = [
    {
        "label": "Units (excl. returns)",
        "value": "'Core Measures'[Units]",
        "type": "units",
    },
]

wishlist_measures = [
    {
        "label": "Wishlist adds",
        "value": "'Core Measures'[Wishlist Additions]",
        "type": "wishlist",
    },
    {
        "label": "Wishlist activations",
        "value": "'Core Measures'[Wishlist Purchases, Activations & Gifts]",
        "type": "wishlist",
    },
    {
        "label": "Wishlist deletes",
        "value": "'Core Measures'[Wishlist Deletes]",
        "type": "wishlist",
    },
    {
        "label": "Non-wishlists Purchases",
        "value": "'Core Measures'[Non-Wishlist Purchases]",
        "type": "wishlist",
    },
]

visibility_measures = [
    {
        "label": "Non-owner impressions",
        "value": "'Core Measures'[Non-owner Impressions]",
        "type": "visibility",
    },
    {
        "label": "Non-owner visits",
        "value": "'Core Measures'[Non-owner Visits]",
        "type": "visibility",
    },
]

all_measures = (
    revenue_measures + units_measures + wishlist_measures + visibility_measures
)


@shared_router.get("/slicer/{slicer_name}")
async def get_slicer_data(
    slicer_name: Slicer,
    shard: UnboundShardDependency,
    dataset_api: ScopedAioDatasetAPIDependency,
):
    if slicer_name == Slicer.SELECTED_MEASURE_ALL:
        return all_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_REVENUE:
        return revenue_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_UNITS:
        return units_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_UNITS_EXCL_RETURNS:
        return units_measures_excl_returns
    elif slicer_name == Slicer.SELECTED_MEASURE_WISHLIST:
        return wishlist_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_VISIBILITY:
        return visibility_measures
    elif slicer_name == Slicer.SALE_DAY_TYPE:
        return ["promo", "regular"]

    # TODO: load shard here!
    if slicer_name == Slicer.STUDIO:
        query = "EVALUATE dim_studio"
        return list(
            map(
                _convert_studio_from_dax,
                await dataset_api.execute_dax(
                    str(shard.workspace_id),
                    str(shard.dataset_id),
                    query,
                ),
            )
        )
    elif slicer_name == Slicer.REGION:
        query = """
        EVALUATE
        SELECTCOLUMNS( dim_country, dim_country[region], dim_country[country_name] )
        ORDER BY
            dim_country[region],
            dim_country[country_name]
        """
        return list(
            map(
                _convert_region_from_dax,
                await dataset_api.execute_dax(
                    str(shard.workspace_id),
                    str(shard.dataset_id),
                    query,
                ),
            )
        )
    else:
        raise Exception("Unsupported slicer")


@shared_router.post("/query")
async def get_query_result(
    body: DaxQuery,
    shard: UnboundShardDependency,
    dataset_api: ScopedAioDatasetAPIDependency,
):
    return await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        body.query,
    )


@private_router.post("/permission-set", response_model=PermissionSet)
def create_permission_set(
    repo: RepoDependency,
    studio_id: StudioIdDependency,
    new_permission_set: CreatePermissionSet,
):
    permission_set = repo.permission_set.create(new_permission_set=new_permission_set)

    if repo.profile.exists_for_studio(studio_id=studio_id):
        profile = repo.profile.get_for_studio(studio_id=studio_id)
        if profile.permission_set_uuid == permission_set.uuid:
            log.info(
                "Skipping creation of new release for studio %s, and permission set %s",
                studio_id,
                permission_set.uuid,
            )
            return permission_set
        version = repo.version.get(profile.active_version_id)
    else:
        version = repo.version.default()

    release = repo.release.create(
        new_release=CreateRelease(
            studio_id=studio_id,
            version_id=version.id,
            permission_set_uuid=permission_set.uuid,
            is_full_recreate=False,
        )
    )

    log.info(f"Requested new release {release}")

    return permission_set


@private_router.get("/shard/active")
def get_active_shard(shard: UnboundShardDependency) -> Shard:
    return shard
