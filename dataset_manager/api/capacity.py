from fastapi import APIRouter
from pydantic import BaseModel

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    CapacitiesAPIDependency,
    ManagementCapacitiesAPIDependency,
    SessionDependency,
)
from dataset_manager.logic.capacity import (
    add_capacity,
    pause_capacity,
    resume_capacity,
    scale_down_capacity,
    scale_up_capacity,
    set_default_capacity,
    set_default_capacity_for_releases,
)
from dataset_manager.repo.capacity import (
    Capacity,
    CapacityName,
    CapacityRepo,
)

router = APIRouter(prefix="/capacity", tags=[ApiTag.PRIVATE])


class CapacityCreate(BaseModel):
    capacity_name: CapacityName


@router.get("", response_model=list[Capacity])
def list_capacities(session: SessionDependency):
    return CapacityRepo(session=session).all()


@router.post("", response_model=Capacity)
def add(
    capacity: CapacityCreate,
    session: SessionDependency,
    capacity_api: CapacitiesAPIDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
):
    return add_capacity(
        session,
        capacity.capacity_name,
        capacity_api,
        management_capacities_api,
    )


@router.put("/{capacity_name}/scale_up", response_model=Capacity)
def scale_up(
    capacity_name: CapacityName,
    session: SessionDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
):
    return scale_up_capacity(session, capacity_name, management_capacities_api)


@router.put("/{capacity_name}/scale_down", response_model=scale_down_capacity)
def scale_down(
    capacity_name: CapacityName,
    session: SessionDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
):
    return scale_down_capacity(session, capacity_name, management_capacities_api)


@router.put("/{capacity_name}/pause", response_model=Capacity)
def pause(
    capacity_name: CapacityName,
    session: SessionDependency,
    capacity_api: CapacitiesAPIDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
):
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    return pause_capacity(session, capacity, capacity_api, management_capacities_api)


@router.put("/{capacity_name}/resume", response_model=Capacity)
def resume(
    capacity_name: CapacityName,
    session: SessionDependency,
    capacity_api: CapacitiesAPIDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
):
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    return resume_capacity(session, capacity, capacity_api, management_capacities_api)


@router.put("/{capacity_name}/set_default", response_model=Capacity)
def set_default(capacity_name: CapacityName, session: SessionDependency):
    return set_default_capacity(session, capacity_name)


@router.put(
    "/{capacity_name}/set_default_for_releases",
    response_model=Capacity,
    tags=[ApiTag.PRIVATE],
)
def set_default_for_releases(capacity_name: CapacityName, session: SessionDependency):
    return set_default_capacity_for_releases(session, capacity_name)


@router.delete("/{capacity_name}", response_model=None)
def delete(
    capacity_name: CapacityName,
    session: SessionDependency,
    capacity_api: CapacitiesAPIDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
):
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    pause_capacity(session, capacity, capacity_api, management_capacities_api)
    CapacityRepo(session=session).delete(capacity_name)
