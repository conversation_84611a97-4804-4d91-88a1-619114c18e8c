import logging

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import SessionDependency
from dataset_manager.api.studio.router import router
from dataset_manager.repo.permission_set import (
    CreatePermissionSet,
    PermissionSet,
    PermissionSetRepo,
)
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.release import CreateRelease, ReleaseRepo
from dataset_manager.repo.version import VersionRepo

log = logging.getLogger(__name__)


@router.post(
    "/{studio_id}/permission-set", response_model=PermissionSet, tags=[ApiTag.PRIVATE]
)
def create_permission_set(
    studio_id: int,
    new_permission_set: CreatePermissionSet,
    session: SessionDependency,
):
    permission_set = PermissionSetRepo(session=session).create(
        new_permission_set=new_permission_set
    )

    profile_repo = ProfileRepo(session=session)
    version_repo = VersionRepo(session=session)

    if profile_repo.exists_for_studio(studio_id=studio_id):
        profile = profile_repo.get_for_studio(studio_id=studio_id)
        if profile.permission_set_uuid == permission_set.uuid:
            log.info(
                "Skipping creation of new release for studio %s, and permission set %s",
                studio_id,
                permission_set.uuid,
            )
            return permission_set
        version = version_repo.get(profile.active_version_id)
    else:
        version = version_repo.default()

    release = ReleaseRepo(session=session).create(
        new_release=CreateRelease(
            studio_id=studio_id,
            version_id=version.id,
            permission_set_uuid=permission_set.uuid,
            is_full_recreate=False,
        )
    )

    log.info(f"Requested new release {release}")

    return permission_set
