import logging

from dataset_manager.api.api_tags import Api<PERSON>ag
from dataset_manager.api.dependencies import ProfileAPIDependency, SessionDependency
from dataset_manager.api.studio.router import router
from dataset_manager.repo.profile import Profile, ProfileRepo

log = logging.getLogger(__name__)


@router.delete("/{studio_id}", response_model=Profile, tags=[ApiTag.PRIVATE])
def delete_profile(
    studio_id: int,
    profile_api: ProfileAPIDependency,
    session: SessionDependency,
):
    profiles = ProfileRepo(session=session)
    profile = profiles.get_for_studio(studio_id=studio_id)
    profile_api.delete(profile.profile_id)
    return profiles.delete(profile=profile)
