import logging
from enum import Enum
from typing import Annotated

from fastapi import APIR<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Query
from fastapi.concurrency import run_in_threadpool
from pydantic import BaseModel

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dataset import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dataset_manager.api.dependencies import (
    FullShardDependency,
    RepoDependency,
    ScopedDatasetAPIDependency,
    ScopedWorkspaceAPIDependency,
    SessionContextDependency,
    SessionDependency,
    get_scoped_aio_dataset_api,
)
from dataset_manager.azure.powerbi.datasets import PBIRefresh
from dataset_manager.config import SettingsDependency, get_config
from dataset_manager.entities import ShardWorkspaceId
from dataset_manager.logic.shard import assign_to_capacity
from dataset_manager.repo.capacity import CapacityName
from dataset_manager.repo.permission_set import Permission
from dataset_manager.repo.profile import Profile
from dataset_manager.repo.shard import Shard, ShardRepo

log = logging.getLogger(__name__)
router = APIRouter(prefix="/shard", tags=[ApiTag.PRIVATE])


class AssignCapacity(BaseModel):
    capacity_name: CapacityName


class RefreshType(Enum):
    FULL = "full"
    INCREMENTAL = "incremental"


@router.get("/active", response_model=list[Shard])
def get_shard_list_by_given_studios(
    repo: RepoDependency,
    studio_ids: Annotated[list[int] | None, Query()] = None,
):
    result = repo.shard.search(studio_ids=studio_ids)
    return sorted(result, key=lambda shard: shard.workspace_id)


@router.get("/{shard_id}", response_model=Shard)
def get_one_shard(
    shard_id: ShardWorkspaceId,
    session: SessionDependency,
):
    return ShardRepo(session=session).get_by_workspace_id(workspace_id=shard_id)


@router.get("/{shard_id}/profiles", response_model=list[Profile])
def get_shard_profiles(shard_id: ShardWorkspaceId, repo: RepoDependency):
    shard = repo.shard.get_by_workspace_id(workspace_id=shard_id)
    return repo.profile.get_all_for_permission_set_uuid(
        permission_set_uuid=shard.permission_set_uuid
    )


@router.post("/{shard_id}/query")
async def post_shard_dax_query(
    settings: SettingsDependency,
    shard_id: ShardWorkspaceId,
    body: DaxQuery,
    session_context: SessionContextDependency,
):
    def _get_shard():
        with session_context() as session:
            return ShardRepo(session=session).get_by_workspace_id(workspace_id=shard_id)

    shard = await run_in_threadpool(_get_shard)
    dataset_api = get_scoped_aio_dataset_api(shard, settings)
    return await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        body.query,
    )


@router.put("/{workspace_id}/assign-to-capacity", response_model=Shard)
def assign_shard_to_capacity(
    workspace_id: ShardWorkspaceId,
    capacity_to_assign: AssignCapacity,
    repo: RepoDependency,
    shard: FullShardDependency,
    workspace_api: ScopedWorkspaceAPIDependency,
):
    capacity = repo.capacity.get_by_name(name=capacity_to_assign.capacity_name)
    return assign_to_capacity(
        repo=repo, workspace_api=workspace_api, shard=shard, capacity=capacity
    )


@router.put("/{workspace_id}/add-debug-admins")
def add_debug_admins(
    workspace_id: ShardWorkspaceId,
    workspace_api: ScopedWorkspaceAPIDependency,
    config=Depends(get_config),
):
    workspace_api.assign_admins(workspace_id=workspace_id, admins=config.DEBUG_ADMINS)


@router.get("/{workspace_id}/refresh-status", response_model=PBIRefresh)
def refresh_shard_status(
    workspace_id: ShardWorkspaceId,
    repo: RepoDependency,
    shard: FullShardDependency,
    dataset_api: ScopedDatasetAPIDependency,
):
    last_refresh = dataset_api.last_refresh(
        workspace_id=workspace_id, dataset_id=shard.dataset_id
    )
    if last_refresh.status.is_completed():
        repo.shard.save(
            shard.model_copy(update={"last_refresh_timestamp": last_refresh.end_time})
        )
    return last_refresh


@router.post("/{workspace_id}/refresh/{refresh_type}")
def refresh_shard(
    shard: FullShardDependency,
    dataset_api: ScopedDatasetAPIDependency,
    workspace_id: ShardWorkspaceId,
    refresh_type: RefreshType,
    ignore: Annotated[str | None, Header()] = None,
):
    if ignore is not None:  # Ignore refresh from NDP2 until full migration
        return

    allow_incremental = refresh_type == RefreshType.INCREMENTAL
    dataset_api.start_refresh(
        workspace_id=workspace_id,
        dataset_id=shard.dataset_id,
        allow_incremental=allow_incremental,
    )


@router.get("/{shard_id}/permission-set", response_model=list[Permission])
def get_shard_permission_set(shard_id: ShardWorkspaceId, repo: RepoDependency):
    shard = repo.shard.get_by_workspace_id(workspace_id=shard_id)
    return repo.permission_set.get(uuid=shard.permission_set_uuid).permission_set
