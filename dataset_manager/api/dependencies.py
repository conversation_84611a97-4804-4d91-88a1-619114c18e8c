from functools import partial
from typing import Annotated, Callable, ContextManager

from azure.storage.blob import BlobServiceClient
from fastapi import Depends, Request
from sqlalchemy.orm import Session

from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.client import (
    AzureAPIClient,
    aio_auth_token_provider,
    auth_token_provider,
)
from dataset_manager.azure.identity import CredentialsContext, credential
from dataset_manager.azure.management.capacities import (
    ManagementCapacitiesAPI,
    get_management_capacities_api,
)
from dataset_manager.azure.powerbi.capacities import CapacitiesAPI
from dataset_manager.azure.powerbi.datasets import (
    AioDatasetAPI,
    DatasetAPI,
    aio_get_dataset_api,
    get_dataset_api,
)
from dataset_manager.azure.powerbi.embed import EmbedAPI
from dataset_manager.azure.powerbi.imports import ImportsAPI
from dataset_manager.azure.powerbi.profiles import ProfileAPI, get_profile_api
from dataset_manager.azure.powerbi.reports import ReportsAPI, get_reports_api
from dataset_manager.azure.powerbi.workspaces import <PERSON><PERSON><PERSON><PERSON>, get_workspace_api
from dataset_manager.config import (
    SettingsDependency,
    get_config,
)
from dataset_manager.connectors.db_engine import get_session, get_session_context
from dataset_manager.connectors.user_service import (
    get_legacy_studio_id_from_user_id,
    get_legacy_studio_id_from_user_id_sync,
)
from dataset_manager.entities import SimplifiedShard
from dataset_manager.repo import Repo
from dataset_manager.repo import get_repo as _get_repo
from dataset_manager.repo.shard import Shard, ShardRepo

SessionDependency = Annotated[Session, Depends(get_session)]


def get_repo(session: SessionDependency) -> Repo:
    return _get_repo(session=session)


RepoDependency = Annotated[Repo, Depends(get_repo)]

SessionContextDependency = Annotated[
    Callable[[], ContextManager[Session]], Depends(get_session_context)
]


def get_management_auth_token_provider(settings: SettingsDependency):
    return partial(auth_token_provider, settings.azure_mng_token_url)


ManagementAuthTokenProviderDependency = Annotated[
    Callable, Depends(get_management_auth_token_provider)
]


def get_management_capacities_api_dependency(
    management_auth_token_provider: ManagementAuthTokenProviderDependency,
    config=Depends(get_config),
) -> ManagementCapacitiesAPI:
    return get_management_capacities_api(
        config=config, auth_token_provider=management_auth_token_provider
    )


ManagementCapacitiesAPIDependency = Annotated[
    ManagementCapacitiesAPI, Depends(get_management_capacities_api_dependency)
]


def get_powerbi_auth_token_provider(settings: SettingsDependency):
    return partial(auth_token_provider, settings.pbi_token_url)


PowerbiAuthTokenProviderDependency = Annotated[
    Callable, Depends(get_powerbi_auth_token_provider)
]


def get_powerbi_aio_auth_token_provider(settings: SettingsDependency):
    return partial(aio_auth_token_provider, settings.pbi_token_url)


def get_capacities_api(
    powerbi_auth_token_provider: PowerbiAuthTokenProviderDependency,
) -> CapacitiesAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=powerbi_auth_token_provider)
    return CapacitiesAPI(azure_api_client=azure_api_client)


CapacitiesAPIDependency = Annotated[CapacitiesAPI, Depends(get_capacities_api)]


def get_workspace_api_dependency(
    powerbi_auth_token_provider: PowerbiAuthTokenProviderDependency,
) -> WorkspaceAPI:
    return get_workspace_api(auth_token_provider=powerbi_auth_token_provider)


WorkspaceAPIDependency = Annotated[WorkspaceAPI, Depends(get_workspace_api_dependency)]


def get_profile_api_dependency(
    powerbi_auth_token_provider: PowerbiAuthTokenProviderDependency,
) -> ProfileAPI:
    return get_profile_api(auth_token_provider=powerbi_auth_token_provider)


ProfileAPIDependency = Annotated[ProfileAPI, Depends(get_profile_api_dependency)]


def get_aio_dataset_api_dependency(
    powerbi_auth_token_provider=Depends(get_powerbi_aio_auth_token_provider),
) -> AioDatasetAPI:
    return aio_get_dataset_api(auth_token_provider=powerbi_auth_token_provider)


def get_model_container(config=Depends(get_config)):
    return ModelContainer(
        client=BlobServiceClient(
            account_url=config.STORAGE_ACCOUNT_URL, credential=credential
        )
    )


def get_shard_by_workspace_id(workspace_id, session_context):
    with session_context() as session:
        shard_repo = ShardRepo(session=session)
        return shard_repo.get_by_workspace_id(workspace_id=workspace_id)


def get_shard_by_studio_id(studio_id, session_context):
    with session_context() as session:
        shard_repo = ShardRepo(session=session)
        return shard_repo.get_for_studio_id_and_version(studio_id=studio_id)


def get_shard(
    request: Request,
    session_context: SessionContextDependency,
) -> Shard | SimplifiedShard:
    if "shard" in request.scope:
        # demo mode simplified shard
        return request.scope["shard"]

    studio_id = request.path_params.get("studio_id")
    user_id = request.path_params.get("user_id")
    workspace_id = request.path_params.get("workspace_id")

    if user_id is not None:
        studio_id = get_legacy_studio_id_from_user_id_sync(user_id)
    if studio_id is not None:
        return get_shard_by_studio_id(studio_id, session_context)
    if workspace_id is not None:
        return get_shard_by_workspace_id(workspace_id, session_context)

    raise Exception("No studio_id or workspace_id provided for shard dependency")


ShardDependency = Annotated[Shard | SimplifiedShard, Depends(get_shard)]


def get_full_shard(shard: ShardDependency) -> Shard:
    assert isinstance(shard, Shard), "Demo mode is not supported for this endpoint"
    return shard


FullShardDependency = Annotated[Shard, Depends(get_full_shard)]


def get_scoped_dataset_api(shard: ShardDependency, settings: SettingsDependency):
    assert isinstance(shard, Shard), "Demo mode is not supported for this endpoint"

    powerbi_auth_token_provider = partial(
        auth_token_provider,
        settings.pbi_token_url,
        credentials_context=CredentialsContext(str(shard.permission_set_uuid)),
    )
    return get_dataset_api(auth_token_provider=powerbi_auth_token_provider)


ScopedDatasetAPIDependency = Annotated[DatasetAPI, Depends(get_scoped_dataset_api)]


def get_scoped_workspace_api_dependency(
    shard: ShardDependency, settings: SettingsDependency
) -> WorkspaceAPI:
    assert isinstance(shard, Shard), "Demo mode is not supported for this endpoint"

    powerbi_auth_token_provider = partial(
        auth_token_provider,
        settings.pbi_token_url,
        credentials_context=CredentialsContext(str(shard.permission_set_uuid)),
    )
    return get_workspace_api(auth_token_provider=powerbi_auth_token_provider)


ScopedWorkspaceAPIDependency = Annotated[
    WorkspaceAPI, Depends(get_scoped_workspace_api_dependency)
]


def get_scoped_embed_api(
    shard: ShardDependency,
    settings: SettingsDependency,
    powerbi_auth_token_provider: PowerbiAuthTokenProviderDependency,
) -> EmbedAPI:
    if isinstance(shard, SimplifiedShard):
        powerbi_auth_token_provider = powerbi_auth_token_provider
    else:
        powerbi_auth_token_provider = partial(
            auth_token_provider,
            settings.pbi_token_url,
            credentials_context=CredentialsContext(str(shard.permission_set_uuid)),
        )

    azure_api_client = AzureAPIClient(auth_token_provider=powerbi_auth_token_provider)
    return EmbedAPI(azure_api_client=azure_api_client)


ScopedEmbedAPIDependency = Annotated[EmbedAPI, Depends(get_scoped_embed_api)]


async def get_studio_id(request: Request) -> int:
    if "studio_id" in request.scope:
        return request.scope["studio_id"]

    user_id = request.path_params["user_id"]
    return await get_legacy_studio_id_from_user_id(user_id)


StudioIdDependency = Annotated[int, Depends(get_studio_id)]


def get_unbound_shard(
    request: Request,
    session_context: SessionContextDependency,
    studio_id: StudioIdDependency,
) -> Shard:
    if "shard" in request.scope:
        return request.scope["shard"]

    with session_context() as session:
        return ShardRepo(session=session).get_for_studio_id_and_version(
            studio_id=studio_id
        )


UnboundShardDependency = Annotated[Shard, Depends(get_unbound_shard)]


def get_imports_api(
    powerbi_auth_token_provider: PowerbiAuthTokenProviderDependency,
) -> ImportsAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=powerbi_auth_token_provider)
    return ImportsAPI(azure_api_client=azure_api_client)


ImportsAPIDependency = Annotated[ImportsAPI, Depends(get_imports_api)]


def get_reports_api_dependency(
    powerbi_auth_token_provider: PowerbiAuthTokenProviderDependency,
) -> ReportsAPI:
    return get_reports_api(auth_token_provider=powerbi_auth_token_provider)


ReportsAPIDependency = Annotated[ReportsAPI, Depends(get_reports_api_dependency)]


def get_scoped_aio_dataset_api(
    shard: UnboundShardDependency, settings: SettingsDependency
):
    if isinstance(shard, SimplifiedShard):
        powerbi_auth_token_provider = partial(
            aio_auth_token_provider, settings.pbi_token_url
        )
    else:
        powerbi_auth_token_provider = partial(
            aio_auth_token_provider,
            settings.pbi_token_url,
            credentials_context=CredentialsContext(str(shard.permission_set_uuid)),
        )
    return aio_get_dataset_api(auth_token_provider=powerbi_auth_token_provider)


ScopedAioDatasetAPIDependency = Annotated[
    AioDatasetAPI, Depends(get_scoped_aio_dataset_api)
]
