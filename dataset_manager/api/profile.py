import logging

from fastapi import APIRouter

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import RepoDependency
from dataset_manager.repo.profile import Profile

log = logging.getLogger(__name__)
router = APIRouter(prefix="/profile", tags=[ApiTag.PRIVATE])


@router.get("/all", response_model=list[Profile])
def get_all_profiles(repo: RepoDependency):
    return repo.profile.get_all()
