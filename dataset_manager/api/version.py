import logging
from enum import Enum
from pathlib import Path
from typing import Annotated

from fastapi import APIRouter, Depends, File, Form, UploadFile

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    ImportsAPIDependency,
    RepoDependency,
    ReportsAPIDependency,
    SessionDependency,
    WorkspaceAPIDependency,
    get_model_container,
)
from dataset_manager.azure.blob import ModelContainer
from dataset_manager.config import SettingsDependency
from dataset_manager.logic.version import (
    NewVersionRequest,
    NewVersionUseCase,
    PBIXFile,
    set_default_version,
)
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.release import CreateRelease, ReleaseRepo
from dataset_manager.repo.version import ShardVersion, Version, VersionId, VersionRepo

log = logging.getLogger(__name__)
router = APIRouter(prefix="/version", tags=[ApiTag.PRIVATE])


@router.get("/default", response_model=Version)
def get_version(session: SessionDependency):
    return VersionRepo(session=session).default()


@router.get("/all", response_model=list[Version])
def get_all_versions(session: SessionDependency):
    return VersionRepo(session=session).get_all()


@router.get("/{version_id}", response_model=Version)
def get_specific_version(session: SessionDependency, version_id: VersionId):
    return VersionRepo(session=session).get(version_id=version_id)


class ReleaseMode(str, Enum):
    dev_recreate = "dev_recreate"
    force_recreate = "force_recreate"


@router.put("/{version_id}/release_for/all", response_model=Version)
@router.put("/{version_id}/release_for/all/{release_mode}", response_model=Version)
def version_release_for_all_studios(
    session: SessionDependency,
    version_id: VersionId,
    release_mode: ReleaseMode = ReleaseMode.dev_recreate,
):
    version = VersionRepo(session=session).get(version_id=version_id)

    release_repo = ReleaseRepo(session=session)

    if release_mode == ReleaseMode.force_recreate:
        is_full_recreate = True
    else:
        is_full_recreate = version.full_recreate_on_release

    new_releases = []
    for profile in ProfileRepo(session=session).get_all():
        assert profile.permission_set_uuid is not None
        new_release = CreateRelease(
            studio_id=profile.studio_id,
            version_id=version.id,
            permission_set_uuid=profile.permission_set_uuid,
            is_full_recreate=is_full_recreate,
        )
        new_releases.append(new_release)
    release_repo.create_bulk(new_releases=new_releases)

    return version


@router.put("/{version_id}/release_for", response_model=Version)
@router.put("/{version_id}/release_for/{release_mode}", response_model=Version)
def version_release_for(
    session: SessionDependency,
    version_id: VersionId,
    studio_ids: list[int],
    release_mode: ReleaseMode = ReleaseMode.dev_recreate,
):
    version = VersionRepo(session=session).get(version_id=version_id)

    profile_repo = ProfileRepo(session=session)
    release_repo = ReleaseRepo(session=session)

    if release_mode == ReleaseMode.force_recreate:
        is_full_recreate = True
    else:
        is_full_recreate = version.full_recreate_on_release

    new_releases = []
    for studio_id in studio_ids:
        profile = profile_repo.get_for_studio(studio_id=studio_id)
        assert profile.permission_set_uuid is not None
        new_release = CreateRelease(
            studio_id=profile.studio_id,
            version_id=version.id,
            permission_set_uuid=profile.permission_set_uuid,
            is_full_recreate=is_full_recreate,
        )
        new_releases.append(new_release)
    release_repo.create_bulk(new_releases=new_releases)

    return VersionRepo(session=session).save(version)


@router.put("/{version_id}/set_default", response_model=Version)
def set_default(version_id: VersionId, session: SessionDependency):
    return set_default_version(session, version_id)


@router.post("/{version_id}", response_model=Version)
def post_new_version(
    settings: SettingsDependency,
    reports_api: ReportsAPIDependency,
    imports_api: ImportsAPIDependency,
    workspace_api: WorkspaceAPIDependency,
    repo: RepoDependency,
    version_id: VersionId,
    shard_version: Annotated[ShardVersion | None, Form()] = None,
    dataset: Annotated[UploadFile | None, File()] = None,
    visuals: Annotated[
        list[UploadFile], File(description="Multiple files as UploadFile")
    ] = [],
    model_container: Annotated[ModelContainer, Depends(get_model_container)],
):
    pbix_dataset = (
        PBIXFile(name=dataset.filename, content=dataset.file.read())
        if dataset and dataset.filename is not None
        else None
    )

    assert all(visual.filename is not None for visual in visuals)

    pbix_visuals = []  # Can't use list comprehension because mypy complains about type
    for visual in visuals:
        assert visual.filename is not None
        pbix_visuals.append(PBIXFile(name=visual.filename, content=visual.file.read()))

    request = NewVersionRequest(
        version_id=version_id,
        shard_version=ShardVersion(shard_version or version_id),
        dataset=pbix_dataset,
        visuals=pbix_visuals,
        workspaces_prefix=settings.env_prefix + settings.env,
        desktop_definitions_template_path=Path(
            "static/desktop-definitions-template.json"
        ),
        workspace_admins_credentials=settings.pbi_credentials,
    )

    response = NewVersionUseCase(
        repo=repo,
        reports_api=reports_api,
        imports_api=imports_api,
        workspace_api=workspace_api,
        model_container=model_container,
        request=request,
    ).process()
    return response.version
