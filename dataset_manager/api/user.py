import logging

from fastapi import APIRouter

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    ProfileAPIDependency,
    SessionDependency,
)
from dataset_manager.connectors.user_service import (
    get_legacy_studio_id_from_user_id_sync,
)
from dataset_manager.repo.profile import Profile, ProfileRepo

log = logging.getLogger(__name__)
router = APIRouter(prefix="/user", tags=[ApiTag.PUBLIC])


@router.delete(
    "/{user_id}",
    response_model=Profile,
    description="""Removes users from Dataset Manager. Deletes the profile form PowerBI and marks the profile as deleted in the database.
Endpoint used for deboarding users.""",
)
def delete_profile(
    user_id: str,
    profile_api: ProfileAPIDependency,
    session: SessionDependency,
):
    studio_id = get_legacy_studio_id_from_user_id_sync(user_id)

    profiles = ProfileRepo(session=session)
    profile = profiles.get_for_studio(studio_id=studio_id)
    profile_api.delete(profile.profile_id)
    return profiles.delete(profile=profile)
