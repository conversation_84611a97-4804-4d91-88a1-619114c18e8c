import logging
import os
import time
from datetime import datetime
from typing import Annotated, Any, Dict

import psutil
from azure.core.exceptions import AzureError
from azure.storage.blob import ContainerClient
from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, Depends
from sqlalchemy import exc, func, select, table

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    CapacitiesAPIDependency,
    ManagementCapacitiesAPIDependency,
    SessionDependency,
)
from dataset_manager.azure.blob import MODELS_CONTAINER_NAME
from dataset_manager.azure.exceptions import PBIError
from dataset_manager.azure.identity import credential
from dataset_manager.config import (
    APP_VERSION,
    DOCKER_BUILD_TIMESTAMP,
    DOCKER_TAG,
    HEALTHCHECK_URL,
    SERVICE_NAME,
    STORAGE_ACCOUNT_URL,
)

router = APIRouter(prefix=HEALTHCHECK_URL, tags=[ApiTag.PRIVATE])


# filter healthcheck access logs
class EndpointLogFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find(HEALTHCHECK_URL) == -1


logging.getLogger("uvicorn.access").addFilter(EndpointLogFilter())


@router.get("")
async def health():
    p = psutil.Process(os.getpid()).create_time()
    uptime = relativedelta(datetime.now(), datetime.fromtimestamp(p))
    response: Dict[str, Any] = {}
    response["serviceName"] = SERVICE_NAME
    response["instance"] = os.uname()
    response["version"] = APP_VERSION
    response["docker"] = {}
    response["docker"]["tag"] = DOCKER_TAG
    response["docker"]["buildTimestamp"] = DOCKER_BUILD_TIMESTAMP
    response["uptime"] = {}
    response["uptime"]["seconds"] = uptime.seconds
    response["uptime"]["readable"] = (
        f"{uptime.years} years, {uptime.months} months, {uptime.days} days, {uptime.hours} hours, {uptime.minutes} minutes, {uptime.seconds} seconds"
    )

    return response


def get_container_client():
    with ContainerClient(
        STORAGE_ACCOUNT_URL,
        MODELS_CONTAINER_NAME,
        credential,
    ) as container_client:
        yield container_client


@router.get("/synthetic")
def synthetic_health(
    session: SessionDependency,
    capacities_api: CapacitiesAPIDependency,
    management_capacities_api: ManagementCapacitiesAPIDependency,
    container_client: Annotated[ContainerClient, Depends(get_container_client)],
):
    response: dict[str, bool | float | None] = {
        "database_connected": False,
        "database_duration": None,
        "storage_connected": False,
        "storage_duration": None,
        "pbi_api_available": False,
        "pbi_api_duration": None,
        "azure_api_available": False,
        "azure_api_duration": None,
    }
    try:
        start = time.perf_counter()
        tables_count = session.scalar(
            select(func.count()).select_from(  # pylint: disable=not-callable
                table("tables", schema="sys")
            )
        )
        response["database_duration"] = time.perf_counter() - start
        response["database_connected"] = bool(tables_count)
    except exc.SQLAlchemyError:
        pass

    try:
        start = time.perf_counter()
        response["storage_connected"] = container_client.exists()
        response["storage_duration"] = time.perf_counter() - start
    except AzureError:
        pass

    try:
        start = time.perf_counter()
        capacities_api.get_all()
        response["pbi_api_duration"] = time.perf_counter() - start
        response["pbi_api_available"] = True
    except Exception:
        pass

    try:
        start = time.perf_counter()
        management_capacities_api.get_all()
        response["azure_api_duration"] = time.perf_counter() - start
        response["azure_api_available"] = True
    except PBIError:
        pass

    return response
