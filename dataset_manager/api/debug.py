from fastapi import APIRouter

from dataset_manager.api.api_tags import Api<PERSON>ag
from dataset_manager.api.dependencies import (
    ManagementCapacitiesAPIDependency,
    RepoDependency,
    ScopedDatasetAPIDependency,
    ShardDependency,
    get_scoped_dataset_api,
)
from dataset_manager.config import SettingsDependency
from dataset_manager.entities import ShardWorkspaceId, StudioId

router = APIRouter(prefix="/debug", tags=[ApiTag.PRIVATE])


@router.get("/studio_id/{studio_id}")
def studio(
    studio_id: StudioId,
    shard: ShardDependency,
    dataset_api: ScopedDatasetAPIDependency,
) -> dict:
    refresh_history = dataset_api.all_refreshes(
        workspace_id=shard.workspace_id, dataset_id=shard.dataset_id
    )
    return {
        "shard": shard,
        "refresh_history": refresh_history,
    }


@router.get("/shard/{shard_id}")
def workspace(
    shard_id: ShardWorkspaceId,
    repo: RepoDependency,
    settings: SettingsDependency,
) -> dict:
    shard = repo.shard.get_by_workspace_id(workspace_id=shard_id)
    dataset_api = get_scoped_dataset_api(shard=shard, settings=settings)
    refresh_history = dataset_api.all_refreshes(
        workspace_id=shard.workspace_id, dataset_id=shard.dataset_id
    )
    return {
        "shard": shard,
        "refresh_history": refresh_history,
    }


@router.get("/capacities")
def capacities(
    repo: RepoDependency, capacities_api: ManagementCapacitiesAPIDependency
) -> dict:
    all_capacities = repo.capacity.all()
    return {
        capacity.name: {
            "db_state": capacity,
            "actual_state": capacities_api.details(capacity.name),
        }
        for capacity in all_capacities
    }
