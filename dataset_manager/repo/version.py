from pydantic import BaseModel, ConfigDict
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>an, Column, String
from sqlalchemy.orm import Session, deferred

from dataset_manager.connectors.db_engine import Base, filter_query
from dataset_manager.entities import (
    DatasetTemplateWorkspaceId,
    ShardVersion,
    VersionId,
    VisualsDatasetId,
    VisualsWorkspaceId,
)


class _DBVersion(Base):
    __tablename__ = "version"
    __table_args__ = {"schema": "WebApp"}
    id = Column(String(40), primary_key=True)
    shard_version = Column("shard_version", String(40), nullable=False)
    shard_template_workspace_id = Column(
        "shard_template_workspace_id", String(40), nullable=False
    )
    visuals_workspace_id = Column("visuals_workspace_id", String(40), nullable=False)
    visuals_dataset_id = Column("visuals_dataset_id", String(40), nullable=False)
    visuals_reports_ids = Column("visuals_reports_ids", JSON, nullable=False)
    visuals_desktop_definitions = deferred(
        Column("visuals_desktop_definitions", JSON, nullable=False)
    )
    is_active = Column("is_active", Boolean, nullable=False)
    is_default = Column("is_default", Boolean, nullable=False)


class VersionNotFound(Exception):
    pass


class Version(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: VersionId
    shard_version: ShardVersion
    shard_template_workspace_id: DatasetTemplateWorkspaceId
    visuals_workspace_id: VisualsWorkspaceId
    visuals_dataset_id: VisualsDatasetId
    visuals_reports_ids: list[str]
    is_active: bool
    is_default: bool

    @property
    def full_recreate_on_release(self):
        return self.id.full_recreate_on_release


class VersionExtended(Version):
    visuals_desktop_definitions: dict


class VersionRepo:
    def __init__(self, session: Session) -> None:
        self._session = session

    def default(self) -> Version:
        orm_version = filter_query(
            self._session, _DBVersion, is_default=True
        ).one_or_none()
        if orm_version is None:
            raise VersionNotFound
        return Version.model_validate(orm_version)

    def get(self, version_id: VersionId) -> Version:
        orm_version = filter_query(
            self._session, _DBVersion, id=version_id
        ).one_or_none()
        if orm_version is None:
            raise VersionNotFound
        return Version.model_validate(orm_version)

    def get_extended(self, version_id: VersionId) -> VersionExtended:
        orm_version = filter_query(
            self._session, _DBVersion, id=version_id
        ).one_or_none()
        if orm_version is None:
            raise VersionNotFound
        return VersionExtended.model_validate(orm_version)

    def get_for_shard_version(self, shard_version: ShardVersion) -> Version:
        orm_version = filter_query(
            self._session, _DBVersion, shard_version=shard_version
        ).first()
        if orm_version is None:
            raise VersionNotFound
        return Version.model_validate(orm_version)

    def get_all(self) -> list[Version]:
        orm_shards = filter_query(self._session, _DBVersion).all()
        return [Version.model_validate(orm_shard) for orm_shard in orm_shards]

    def save(self, version: Version | VersionExtended) -> Version:
        db_version = self._session.get(_DBVersion, ident=version.id) or _DBVersion(
            id=version.id
        )
        db_version.shard_version = version.shard_version
        db_version.shard_template_workspace_id = version.shard_template_workspace_id
        db_version.visuals_workspace_id = version.visuals_workspace_id
        db_version.visuals_dataset_id = version.visuals_dataset_id
        db_version.visuals_reports_ids = version.visuals_reports_ids
        db_version.is_active = version.is_active
        db_version.is_default = version.is_default

        if isinstance(version, VersionExtended):
            db_version.visuals_desktop_definitions = version.visuals_desktop_definitions

        self._session.add(db_version)
        self._session.flush()

        return self.get(version_id=version.id)

    def unset_default_for_all(self) -> list[Version]:
        filter_query(self._session, _DBVersion).update({_DBVersion.is_default: False})
        return self.get_all()
