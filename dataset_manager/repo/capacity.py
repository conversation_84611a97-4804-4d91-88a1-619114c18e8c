from __future__ import annotations

import enum
import logging

from pydantic import BaseModel, ConfigDict
from sqlalchemy import Column
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import <PERSON><PERSON><PERSON>, Enum, String

from dataset_manager.connectors.db_engine import Base, filter_query
from dataset_manager.entities import CapacityId, CapacityName

log = logging.getLogger(__name__)


class CapacityState(str, enum.Enum):
    ACTIVE = "Active"
    PAUSED = "Paused"
    UNKNOWN = "Unknown"

    @classmethod
    def from_raw(cls, raw_state) -> CapacityState:
        if raw_state == "Active":
            return CapacityState.ACTIVE
        elif raw_state in ["Paused", "Suspended"]:
            return CapacityState.PAUSED
        else:
            log.warning("Capacity in unknown state, %s", raw_state)
            return CapacityState.UNKNOWN


class UniqueFlag(str, enum.Enum):
    IS_DEFAULT = "is_default"
    IS_DEFAULT_FOR_RELEASE = "is_default_for_releases"


class Capacity(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: CapacityId
    name: CapacityName
    tier: str
    state: CapacityState
    is_default: bool
    is_default_for_releases: bool

    def set_flag(self, flag: UniqueFlag, value: bool = True):
        setattr(self, flag.value, value)


class _DBCapacity(Base):
    __tablename__ = "capacity"
    __table_args__ = {"schema": "WebApp"}
    id = Column("id", String(40), nullable=False, unique=True, primary_key=True)
    name = Column("name", String(40), nullable=False)
    tier = Column("tier", String(40), nullable=False)
    state = Column("state", Enum(CapacityState), nullable=False)
    is_default = Column("is_default", Boolean, nullable=False)
    is_default_for_releases = Column("is_default_for_releases", Boolean, nullable=False)


class CapacityNotFound(Exception):
    pass


class DefaultCapacityIsNotSet(Exception):
    pass


class CapacityForReleasesIsNotSet(Exception):
    pass


class CapacityRepo:
    def __init__(self, session: Session) -> None:
        self._session = session

    def delete(self, name) -> None:
        orm_capacity = filter_query(self._session, _DBCapacity, name=name).one_or_none()
        if orm_capacity is None:
            raise CapacityNotFound
        self._session.delete(orm_capacity)

    def all(self) -> list[Capacity]:
        orm_shards = filter_query(self._session, _DBCapacity).all()
        return [Capacity.model_validate(orm_shard) for orm_shard in orm_shards]

    def active_not_for_releases(self) -> list[Capacity]:
        orm_shards = filter_query(
            self._session,
            _DBCapacity,
            state=CapacityState.ACTIVE,
            is_default_for_releases=False,
        ).all()
        return [Capacity.model_validate(orm_shard) for orm_shard in orm_shards]

    def get_by_name(self, name: str) -> Capacity:
        orm_capacity = filter_query(self._session, _DBCapacity, name=name).one_or_none()
        if orm_capacity is None:
            raise CapacityNotFound
        return Capacity.model_validate(orm_capacity)

    def default_for_releases(self) -> Capacity:
        orm_capacity = filter_query(
            self._session, _DBCapacity, is_default_for_releases=True
        ).one_or_none()
        if orm_capacity is None:
            raise CapacityForReleasesIsNotSet
        return Capacity.model_validate(orm_capacity)

    def default(self) -> Capacity:
        orm_capacity = filter_query(
            self._session, _DBCapacity, is_default=True
        ).one_or_none()
        if orm_capacity is None:
            raise DefaultCapacityIsNotSet
        return Capacity.model_validate(orm_capacity)

    def save(self, capacity: Capacity) -> Capacity:
        db_version = self._session.get(_DBCapacity, ident=capacity.id) or _DBCapacity(
            id=capacity.id
        )

        db_version.id = capacity.id
        db_version.name = capacity.name
        db_version.state = capacity.state
        db_version.tier = capacity.tier
        db_version.is_default = capacity.is_default
        db_version.is_default_for_releases = capacity.is_default_for_releases

        self._session.add(db_version)
        self._session.flush()

        return self.get_by_name(name=capacity.name)

    def unset_flag_for_all(self, flag: UniqueFlag) -> list[Capacity]:
        filter_query(self._session, _DBCapacity).update({flag.value: False})
        return self.all()
