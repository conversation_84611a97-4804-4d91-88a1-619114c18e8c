import os
import sys
from pathlib import Path
from typing import Annotated

from fastapi import Depends
from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from dataset_manager.entities import (
    DatasetId,
    ShardWorkspaceId,
    VersionId,
)

ENV = os.environ.get("ENV", "local")


class DemoModeSettings(BaseSettings):
    desktop_definitions: Path = Path("static/demo-mode-desktop-definitions.json")
    version: VersionId = VersionId("3.4.1")
    workspace_id: ShardWorkspaceId = ShardWorkspaceId(
        "9c0778ee-6f7c-4e4f-b1ed-294f90bc7887"
    )
    dateset_id: DatasetId = DatasetId("6168dab1-9521-4bca-9f09-504edd812f40")
    reports_ids: list[str] = [
        "7f9ac463-37d7-4f8b-83ee-df9372d00224",
        "0c8f333a-9fc8-4e64-a7d1-84e31b77dd39",
        "185c619a-7c10-40cc-80f1-4cdf20ebd711",
        "d5a26835-019b-4b13-a2c7-b4c3736b0ba4",
        "f3652428-d1bb-4cea-89fd-401112a32d15",
        "d0622735-c6fd-446b-a8b6-ef9ce501eb72",
        "33802f50-6e00-4c5b-85e0-8082691c8a73",
        "f25c22ae-de95-44c6-b788-82f69d17b482",
        "22bb7630-f89f-40cf-b0b9-ed2f6cc42b11",
        "7235ca8c-4ce3-463a-a1ec-1146c04c1050",
        "3b1d367b-a0af-4630-95ec-67e533846940",
        "2502b4bb-1eb6-4b4f-852c-97bf82883f6b",
        "c69545d2-aab4-46f2-a13e-28be8e807d9c",
        "fe705f69-5d4d-4b48-be54-f90b3d3dafa1",
        "e2de3f82-8d41-432e-b29e-beb138acfe00",
        "eb12b5b4-4a7b-4f67-9295-0838f62d523c",
        "272e72cd-b906-418d-bcd9-41585e94a1a9",
    ]


class PBICredentials(BaseSettings):
    index: int
    client_id: str
    client_secret: str
    object_id: str

    @property
    def display_name(self) -> str:
        return f"Service Principal {ENV} {self.index}"


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_nested_delimiter="__")

    demo_mode: DemoModeSettings = DemoModeSettings()

    service_name: str = "dataset-manager"
    pod_namespace: str = "dpt"
    pod_name: str = "dataset-manager"

    lease_duration_seconds: int = 15
    lease_renew_deadline_seconds: int = 10
    lease_renew_period_seconds: int = 2

    env: str = "local"
    env_prefix: str = ""

    pbi_token_url: str = "https://analysis.windows.net/powerbi/api/.default"
    azure_mng_token_url: str = "https://management.azure.com/.default"

    pbi_azure_tenant_id: str
    pbi_credentials: list[PBICredentials] = []

    @field_validator("pbi_credentials", mode="before")
    def validate_endpoints(
        cls, pbi_credentials: dict | list[PBICredentials]
    ) -> list[PBICredentials]:
        if isinstance(pbi_credentials, list):
            return pbi_credentials
        return [
            PBICredentials(index=index, **api_key)
            for index, api_key in enumerate(pbi_credentials.values())
        ]


DEV_USERNAME = os.environ.get("DEV_USERNAME")

# PBI stuff

SERVICE_PRINCIPAL_ID = os.environ.get("SERVICE_PRINCIPAL_ID", "")

DLS_ACCOUNT_NAME = os.environ.get("DLS_ACCOUNT_NAME", "")
PROCESSED_ADLS_URL = f"https://{DLS_ACCOUNT_NAME}.dfs.core.windows.net/"
PROCESSED_ADLS_CREDENTIALS = os.environ.get("PROCESSED_ADLS_CREDENTIALS", "")


AZURE_SUBSCRIPTION_ID = os.environ.get(
    "AZURE_SUBSCRIPTION_ID", "97bd5d65-c018-4632-b6c3-0feeb064b3d1"
)
AZURE_RESOURCE_GROUP_NAME = os.environ.get("AZURE_RESOURCE_GROUP_NAME", "indiebi-dev")


DEBUG_ADMINS = [
    {
        "groupUserAccessRight": "Admin",
        "displayName": "Grzegorz Kocjan",
        "identifier": "<EMAIL>",
        "principalType": "User",
        "emailAddress": "<EMAIL>",
    },
    {
        "groupUserAccessRight": "Admin",
        "displayName": "Aleksander Magda",
        "identifier": "<EMAIL>",
        "principalType": "User",
        "emailAddress": "<EMAIL>",
    },
    {
        "groupUserAccessRight": "Admin",
        "displayName": "Cyprian Krenski",
        "identifier": "<EMAIL>",
        "principalType": "User",
        "emailAddress": "<EMAIL>",
    },
    {
        "groupUserAccessRight": "Admin",
        "displayName": "Marek Pilczuk",
        "identifier": "<EMAIL>",
        "principalType": "User",
        "emailAddress": "<EMAIL>",
    },
    {
        "groupUserAccessRight": "Admin",
        "displayName": "Joanna Szałapska",
        "identifier": "<EMAIL>",
        "principalType": "User",
        "emailAddress": "<EMAIL>",
    },
]

# DB stuff
SQLALCHEMY_DATABASE_URL = os.environ.get(
    "SQLALCHEMY_DATABASE_URL",
    "mssql+pyodbc://indiebi:Password1!@localhost/Shards?driver=ODBC+Driver+17+for+SQL+Server",
)

# DM api key
API_KEY = os.environ.get("API_KEY", "pikachu")


# Consts
PBI_TOKEN_URL = "https://analysis.windows.net/powerbi/api/.default"

ENV_PREFIX = os.environ.get("ENV_PREFIX", "")

APP_VERSION = "0.1.0"
HEALTHCHECK_URL = "/health"
SERVICE_NAME = "dataset-manager"

STORAGE_ACCOUNT_URL = os.environ.get(
    "DATASET_STORAGE_ACCOUNT_URL",
    f"https://dlsdatasetmanagerd{ENV}.blob.core.windows.net",
)

DOCKER_TAG = os.environ.get("DOCKER_TAG", "")
DOCKER_BUILD_TIMESTAMP = os.environ.get("DOCKER_BUILD_TIMESTAMP", "")

DB_TOKEN_URL = "https://database.windows.net//.default"
SQL_COPT_SS_ACCESS_TOKEN = 1256

TRACING_INSTRUMENTATION_KEY = os.environ.get("TRACING_INSTRUMENTATION_KEY")
TRACING_IS_ENABLED = TRACING_INSTRUMENTATION_KEY is not None

ELASTIC_SECRET_TOKEN = os.environ.get("ELASTIC_SECRET_TOKEN")
ELASTIC_SERVICE_URL = os.environ.get("ELASTIC_SERVICE_URL")

USER_SERVICE_URL = os.environ.get("USER_SERVICE_URL", "https://user-service")
USER_SERVICE_KEY = os.environ.get("USER_SERVICE_KEY", "key")


def get_config():
    return sys.modules[
        __name__
    ]  # TODO: remove this workaround with real configuration classes


def get_modern_settings():
    return Settings()  # type: ignore


SettingsDependency = Annotated[Settings, Depends(get_modern_settings)]
