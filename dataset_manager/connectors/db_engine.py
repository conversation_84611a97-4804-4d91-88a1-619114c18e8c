import struct
from contextlib import contextmanager

import pyodbc
from sqlalchemy import create_engine, event
from sqlalchemy.orm import Query, Session, declarative_base, sessionmaker

from dataset_manager.azure.identity import power_bi_credential
from dataset_manager.config import SQLALCHEMY_DATABASE_URL

pyodbc.pooling = False

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    # SQL connection pool size
    pool_size=30,
    # temporarily increase number connections by 25 (to 35) if none are available
    max_overflow=25,
    # recycle connections every hour
    pool_recycle=3600,
    # validate connection before using it
    pool_pre_ping=True,
)

if "odbc_connect" in SQLALCHEMY_DATABASE_URL:
    SQL_COPT_SS_ACCESS_TOKEN = (
        1256  # Connection option for access tokens, as defined in msodbcsql.h
    )
    TOKEN_URL = "https://database.windows.net//.default"  # The token URL for any Azure SQL database

    @event.listens_for(engine, "do_connect")
    def provide_token(dialect, conn_rec, cargs, cparams):
        # remove the "Trusted_Connection" parameter that SQLAlchemy adds
        cargs[0] = cargs[0].replace(";Trusted_Connection=Yes", "")

        # create token credential
        raw_token = power_bi_credential.get_token(TOKEN_URL).token.encode("utf-16-le")
        token_struct = struct.pack(f"<I{len(raw_token)}s", len(raw_token), raw_token)
        # apply it to keyword arguments
        cparams["attrs_before"] = {SQL_COPT_SS_ACCESS_TOKEN: token_struct}


SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)  # type: ignore[call-overload]

Base = declarative_base()


def get_session():
    session = SessionLocal()
    try:
        yield session
    except:
        session.rollback()
        raise
    else:
        session.commit()
    finally:
        session.close()


def get_session_context():
    @contextmanager
    def _session_context():
        db = SessionLocal()
        try:
            yield db
        except:
            db.rollback()
            raise
        else:
            db.commit()
        finally:
            db.close()

    return _session_context


def filter_query(
    session: Session, entity: type[Base], columns: list[str] | None = None, **kwargs
) -> Query:
    query = (
        session.query(*[getattr(entity, col) for col in columns])
        if columns
        else session.query(entity)
    )
    filter_by = {
        key: val if val != "null" else None
        for key, val in kwargs.items()
        if val is not None and not isinstance(val, list)
    }
    filter_in_list = [
        getattr(entity, key[:-1]).in_(val)
        for key, val in kwargs.items()
        if val is not None and isinstance(val, list)
    ]
    return query.filter(*filter_in_list).filter_by(**filter_by)
