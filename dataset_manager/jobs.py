import asyncio
import functools
import logging
import signal
from collections.abc import Callable
from concurrent.futures import FIRST_COMPLETED, ThreadPoolExecutor, wait
from typing import ParamSpec, TypeVar

import anyio.to_thread

from dataset_manager.leader_election import LeaderElection

log = logging.getLogger(__name__)

P = ParamSpec("P")
T = TypeVar("T")


async def _run_in_threadpool(
    request_func: Callable[P, T], *args: P.args, **kwargs: P.kwargs
) -> T:
    func = functools.partial(request_func, *args, **kwargs)
    return await anyio.to_thread.run_sync(func)


async def run_forever_as_leader_in_executor(
    leader_election: LeaderElection,
    worker_fn: Callable[[T], None],
    get_next_jobs_fn: Callable[[int, list[T]], list[T]],
    max_parallel_jobs: int = 10,
    thread_name_prefix: str = "",
    idle_sleep_seconds: float = 10.0,
    delay_on_start_seconds: int = 60,
):
    await asyncio.sleep(delay_on_start_seconds)

    stop_event = asyncio.Event()

    def termination_handler(_signum, _frame):
        stop_event.set()

    signal.signal(signal.SIGTERM, termination_handler)
    signal.signal(signal.SIGINT, termination_handler)

    jobs_in_progress: dict = {}

    def _remove_job_in_progress(future):
        del jobs_in_progress[future]

    with ThreadPoolExecutor(
        max_workers=max_parallel_jobs, thread_name_prefix=thread_name_prefix
    ) as executor:
        while not stop_event.is_set():
            if leader_election.is_leader:
                jobs_in_progress_count = len(jobs_in_progress)
                if jobs_in_progress_count < max_parallel_jobs:
                    next_jobs = await _run_in_threadpool(
                        get_next_jobs_fn,
                        max_parallel_jobs - jobs_in_progress_count,
                        list(jobs_in_progress.values()),
                    )

                    if len(next_jobs) == 0:
                        await asyncio.sleep(idle_sleep_seconds)
                        continue
                    else:
                        log.info(
                            "Getting next jobs count %s, ignored: %s",
                            max_parallel_jobs - jobs_in_progress_count,
                            [release.uuid for release in jobs_in_progress.values()],
                        )
                        log.info("Scheduling next jobs %s", next_jobs)

                        for next_job in next_jobs:
                            future = executor.submit(worker_fn, next_job)
                            jobs_in_progress[future] = next_job
                            future.add_done_callback(_remove_job_in_progress)

                    await _run_in_threadpool(
                        wait, list(jobs_in_progress.keys()), return_when=FIRST_COMPLETED
                    )
                else:
                    await asyncio.sleep(idle_sleep_seconds)
            else:
                await asyncio.sleep(idle_sleep_seconds)
