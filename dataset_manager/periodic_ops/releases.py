from functools import partial

from azure.storage.blob import BlobServiceClient

from dataset_manager.api.dependencies import get_capacities_api
from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.client import auth_token_provider
from dataset_manager.azure.identity import CredentialsContext, credential
from dataset_manager.azure.management.capacities import get_management_capacities_api
from dataset_manager.azure.powerbi.datasets import get_dataset_api
from dataset_manager.azure.powerbi.profiles import get_profile_api
from dataset_manager.azure.powerbi.workspaces import get_workspace_api
from dataset_manager.config import get_config, get_modern_settings
from dataset_manager.connectors.db_engine import SessionLocal
from dataset_manager.entities import (
    Environment,
    ProcessedAdlsCredentials,
    ProcessedAdlsURL,
    ServicePrincipalId,
)
from dataset_manager.jobs import run_forever_as_leader_in_executor
from dataset_manager.leader_election import LeaderElection
from dataset_manager.logic.release import (
    assign_releases,
    handle_requested_assign,
    handle_requested_shard,
)
from dataset_manager.repo import get_repo
from dataset_manager.repo.release import Release


def assign_requested_releases():
    config = get_config()
    settings = get_modern_settings()

    management_auth_token_provider = partial(
        auth_token_provider, settings.azure_mng_token_url
    )
    management_capacities_api = get_management_capacities_api(
        config=config, auth_token_provider=management_auth_token_provider
    )

    powerbi_auth_token_provider = partial(auth_token_provider, settings.pbi_token_url)
    capacities_api = get_capacities_api(
        powerbi_auth_token_provider=powerbi_auth_token_provider
    )

    with SessionLocal() as session, session.begin():
        repo = get_repo(session=session)
        assign_releases(
            repo=repo,
            capacities_api=capacities_api,
            management_capacities_api=management_capacities_api,
        )


async def handle_requested_assign_concurrently(leader_election: LeaderElection):
    config = get_config()
    settings = get_modern_settings()

    powerbi_auth_token_provider = partial(auth_token_provider, settings.pbi_token_url)
    profile_api = get_profile_api(auth_token_provider=powerbi_auth_token_provider)

    def _worker(release: Release):
        with SessionLocal() as session, session.begin():
            handle_requested_assign(
                repo=get_repo(session=session),
                profile_api=profile_api,
                environment=Environment(config.ENV_PREFIX + config.ENV),
                service_principal_id=ServicePrincipalId(config.SERVICE_PRINCIPAL_ID),
                release=release,
            )

    def _get_next_assign_jobs(limit, jobs_in_progress: list[Release]) -> list[Release]:
        with SessionLocal() as session, session.begin():
            return get_repo(session=session).release.requested_assign(
                limit=limit, ignore_uuids=[release.uuid for release in jobs_in_progress]
            )

    await run_forever_as_leader_in_executor(
        leader_election=leader_election,
        worker_fn=_worker,
        get_next_jobs_fn=_get_next_assign_jobs,
        max_parallel_jobs=5,
        thread_name_prefix="assign_worker",
    )


async def handle_requested_shard_concurrently(leader_election: LeaderElection):
    config = get_config()
    settings = get_modern_settings()

    model_container = ModelContainer(
        client=BlobServiceClient(
            account_url=config.STORAGE_ACCOUNT_URL, credential=credential
        )
    )

    def _worker(release: Release):
        powerbi_auth_token_provider = partial(
            auth_token_provider,
            settings.pbi_token_url,
            credentials_context=CredentialsContext(str(release.permission_set_uuid)),
        )
        workspace_api = get_workspace_api(
            auth_token_provider=powerbi_auth_token_provider
        )
        dataset_api = get_dataset_api(auth_token_provider=powerbi_auth_token_provider)
        with SessionLocal() as session, session.begin():
            handle_requested_shard(
                repo=get_repo(session=session),
                workspace_api=workspace_api,
                dataset_api=dataset_api,
                model_container=model_container,
                environment=Environment(config.ENV_PREFIX + config.ENV),
                processed_adls_url=ProcessedAdlsURL(config.PROCESSED_ADLS_URL),
                processed_adls_credentials=ProcessedAdlsCredentials(
                    config.PROCESSED_ADLS_CREDENTIALS
                ),
                release=release,
            )

    def _get_next_shard_jobs(limit, jobs_in_progress: list[Release]) -> list[Release]:
        with SessionLocal() as session, session.begin():
            return get_repo(session=session).release.requested_shard(
                limit=limit, ignore_uuids=[release.uuid for release in jobs_in_progress]
            )

    await run_forever_as_leader_in_executor(
        leader_election=leader_election,
        worker_fn=_worker,
        get_next_jobs_fn=_get_next_shard_jobs,
        max_parallel_jobs=10,
        thread_name_prefix="shard_worker",
    )
