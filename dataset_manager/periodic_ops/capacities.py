import logging
from functools import partial

from dataset_manager.azure.client import auth_token_provider
from dataset_manager.azure.management.capacities import get_management_capacities_api
from dataset_manager.config import get_config, get_modern_settings
from dataset_manager.connectors.db_engine import SessionLocal
from dataset_manager.repo import get_repo
from dataset_manager.repo.capacity import CapacityState

log = logging.getLogger(__name__)


def check_if_all_capacities_are_unpaused():
    config = get_config()
    settings = get_modern_settings()

    management_auth_token_provider = partial(
        auth_token_provider, settings.azure_mng_token_url
    )
    capacities_api = get_management_capacities_api(
        config=config, auth_token_provider=management_auth_token_provider
    )
    with SessionLocal() as session, session.begin():
        repo = get_repo(session=session)
        all_capacities = repo.capacity.all()
        for cp in all_capacities:
            if cp.state != CapacityState.PAUSED:
                details = capacities_api.details(cp.name)
                if details["properties"]["state"] == CapacityState.PAUSED:
                    log.error(f"Capacity {cp.name} should not be paused")
                    capacities_api.resume(cp.name)
