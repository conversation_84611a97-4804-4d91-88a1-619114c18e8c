import os

from azure.identity import ClientSecretCredential, DefaultAzureCredential
from azure.identity.aio import ClientSecretCredential as AioClientSecretCredential
from azure.identity.aio import DefaultAzureCredential as AioDefaultAzureCredential

from dataset_manager.entities import BaseUUIDField


class CredentialsContext(BaseUUIDField):
    @property
    def index(self) -> int:
        return int(str(self)[0].lower(), 16)


credential = DefaultAzureCredential(logging_enable=True)
aio_credential = AioDefaultAzureCredential(logging_enable=True)

if os.environ.get("PBI_AZURE_TENANT_ID"):
    power_bi_credential: ClientSecretCredential | DefaultAzureCredential = (
        ClientSecretCredential(
            tenant_id=os.environ["PBI_AZURE_TENANT_ID"],
            client_id=os.environ["PBI_AZURE_CLIENT_ID"],
            client_secret=os.environ["PBI_AZURE_CLIENT_SECRET"],
        )
    )
    aio_power_bi_credential: AioClientSecretCredential | AioDefaultAzureCredential = (
        AioClientSecretCredential(
            tenant_id=os.environ["PBI_AZURE_TENANT_ID"],
            client_id=os.environ["PBI_AZURE_CLIENT_ID"],
            client_secret=os.environ["PBI_AZURE_CLIENT_SECRET"],
        )
    )
else:
    power_bi_credential = credential
    aio_power_bi_credential = aio_credential


def _build_sharded_credentials() -> list[ClientSecretCredential]:
    creds: list[ClientSecretCredential] = []

    tenant_id = os.environ["PBI_AZURE_TENANT_ID"]
    for i in range(16):
        client_id = os.environ[f"PBI_CREDENTIALS__{i}__CLIENT_ID"]
        client_secret = os.environ[f"PBI_CREDENTIALS__{i}__CLIENT_SECRET"]

        creds.append(
            ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret,
            )
        )

    return creds


def _aio_build_sharded_credentials() -> list[AioClientSecretCredential]:
    aio_creds: list[AioClientSecretCredential] = []

    tenant_id = os.environ["PBI_AZURE_TENANT_ID"]
    for i in range(16):
        client_id = os.environ[f"PBI_CREDENTIALS__{i}__CLIENT_ID"]
        client_secret = os.environ[f"PBI_CREDENTIALS__{i}__CLIENT_SECRET"]

        aio_creds.append(
            AioClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret,
            )
        )

    return aio_creds


power_bi_sharded_credentials = None
aio_power_bi_sharded_credentials = None


def get_power_bi_credential(context: CredentialsContext):
    global power_bi_sharded_credentials
    if power_bi_sharded_credentials is None:
        power_bi_sharded_credentials = _build_sharded_credentials()
    return power_bi_sharded_credentials[context.index]


def aio_get_power_bi_credential(context: CredentialsContext):
    global aio_power_bi_sharded_credentials
    if aio_power_bi_sharded_credentials is None:
        aio_power_bi_sharded_credentials = _aio_build_sharded_credentials()
    return aio_power_bi_sharded_credentials[context.index]
