from dataset_manager.azure.client import AzureAPIClient


class ProfileAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/profiles"

    def delete(self, profile_id: str) -> None:
        self._api.delete(f"{self._base_url}/{profile_id}")

    def create(self, profile_name: str) -> str:
        profile = self._get_by_name(profile_name)
        if profile:
            return profile[0]["id"]
        response = self._api.post(
            "https://api.powerbi.com/v1.0/myorg/profiles",
            json={"displayName": profile_name},
        )
        data = response.json()
        return data["id"]

    def _get_by_name(self, profile_name: str):
        response = self._api.get("https://api.powerbi.com/v1.0/myorg/profiles")
        return [
            profile
            for profile in response.json()["value"]
            if profile["displayName"] == profile_name
        ]


def get_profile_api(auth_token_provider) -> ProfileAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=auth_token_provider)
    return ProfileAPI(azure_api_client=azure_api_client)
