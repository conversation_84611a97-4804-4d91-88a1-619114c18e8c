from dataset_manager.azure.client import AzureAPIClient


class EmbedAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg"

    def generate_token(
        self,
        dataset_ids: list[str],
        reports_ids: list[str],
        lifetime_in_minutes: int = 0,
    ) -> tuple[str, str]:
        response = self._api.post(
            f"{self._base_url}/GenerateToken",
            json={
                "datasets": [{"id": dataset_id} for dataset_id in dataset_ids],
                "reports": [{"id": report_id} for report_id in reports_ids],
                "lifetimeInMinutes": lifetime_in_minutes,
            },
        )
        token = response.json()
        return token["token"], token["expiration"]
