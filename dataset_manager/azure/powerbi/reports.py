from dataset_manager.azure.client import AzureAPIClient


class ReportsAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/groups"

    def list(self, workspace_id: str) -> list[dict]:
        response = self._api.get(f"{self._base_url}/{workspace_id}/reports")
        return response.json()["value"]


def get_reports_api(auth_token_provider) -> ReportsAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=auth_token_provider)
    return ReportsAPI(azure_api_client=azure_api_client)
