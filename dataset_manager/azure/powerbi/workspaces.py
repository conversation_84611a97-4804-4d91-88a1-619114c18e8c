import logging

import requests

from dataset_manager.azure.client import AzureAPIClient
from dataset_manager.entities import (
    CapacityId,
    ServicePrincipalId,
    WorkspaceId,
    WorkspaceName,
)
from dataset_manager.repo.profile import Profile

log = logging.getLogger(__name__)


class WorkspaceAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/groups"

    def create(self, workspace_name: WorkspaceName) -> WorkspaceId:
        existing_workspace = self._get_by_name(workspace_name)
        if existing_workspace:
            return existing_workspace
        response = self._api.post(self._base_url, json={"name": workspace_name})
        return WorkspaceId(response.json()["id"])

    def assign_admins(self, workspace_id: WorkspaceId, admins) -> None:
        existing_users = self._get_users(workspace_id=workspace_id)
        if existing_users:
            existing_users = [user["identifier"] for user in existing_users]

        for user in admins:
            if user["identifier"] in existing_users:
                log.info(
                    "Skipping creating %s user in %s workspace: user already exists",
                    user["identifier"],
                    workspace_id,
                )
                continue
            self._assign_user_to_workspace(workspace_id, user)

    def assign_profile(
        self,
        workspace_id: WorkspaceId,
        profile: Profile,
        service_principal_id: ServicePrincipalId,
    ) -> None:
        existing_profiles = self._get_users(workspace_id=workspace_id)
        if existing_profiles:
            existing_profiles = [
                user["profile"]["id"] for user in existing_profiles if "profile" in user
            ]
        if profile.profile_id in existing_profiles:
            log.info(
                "Skipping creating %s profile_id in %s workspace: profile_id already exists ",
                profile.profile_id,
                workspace_id,
            )
            return

        self._assign_user_to_workspace(
            workspace_id,
            user={
                "groupUserAccessRight": "Admin",
                "displayName": profile.profile_name,
                "identifier": service_principal_id,
                "principalType": "App",
                "profile": {"id": profile.profile_id},
            },
        )

    def assign_to_capacity(
        self, workspace_id: WorkspaceId, capacity_id: CapacityId
    ) -> None:
        self._api.post(
            f"{self._base_url}/{workspace_id}/AssignToCapacity",
            json={"capacityId": capacity_id},
        )

    def delete_workspace(self, workspace_id: WorkspaceId) -> None:
        try:
            self._api.delete(f"{self._base_url}/{workspace_id}")
        except requests.HTTPError as e:
            already_deleted = e.response.json()["error"]["code"] == "StorageInvalidData"
            log.warning(f"Workspace {workspace_id} already deleted in PowerBI")
            if not already_deleted:
                raise e

    def _get_by_name(self, workspace_name: WorkspaceName) -> WorkspaceId | None:
        value = self._api.get(
            self._base_url,
            params={"$filter": f"name eq '{workspace_name}'"},
        ).json()["value"]
        if value:
            return WorkspaceId(value[0]["id"])
        return None

    def _get_users(self, workspace_id: WorkspaceId) -> list:
        response = self._api.get(f"{self._base_url}/{workspace_id}/users")
        return response.json()["value"]

    def _assign_user_to_workspace(self, workspace_id: WorkspaceId, user):
        self._api.post(f"{self._base_url}/{workspace_id}/users", json=user)

    def _delete_profile(self, workspace_id, identifier, profile_id):
        self._api.delete(
            f"{self._base_url}/{workspace_id}/users/{identifier}?profileId={profile_id}"
        )


def get_workspace_api(auth_token_provider) -> WorkspaceAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=auth_token_provider)
    return WorkspaceAPI(azure_api_client=azure_api_client)
