from dataset_manager.azure.client import AzureAPIClient
from dataset_manager.entities import CapacityId
from dataset_manager.repo.capacity import CapacityName, CapacityState


class CapacitiesAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/capacities"

    def get_all(self) -> list[dict]:
        response = self._api.get(self._base_url)
        return response.json()["value"]

    def get_id_by_name(self, c_name: CapacityName) -> CapacityId:
        capacities = self.get_all()
        for capacity in capacities:
            if capacity["displayName"] == c_name:
                return CapacityId(capacity["id"])
        raise Exception(f"Capacity {c_name} not found")

    def get_state_by_name(self, c_name: CapacityName) -> CapacityState:
        capacities = self.get_all()
        for capacity in capacities:
            if capacity["displayName"] == c_name:
                return CapacityState.from_raw(capacity["state"])
        raise Exception(f"Capacity {c_name} not found")
