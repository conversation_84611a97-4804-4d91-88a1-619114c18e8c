import io
import json
from time import sleep
from zipfile import ZIP_DEFLATED, ZipFile

from requests_toolbelt import MultipartEncoder

from dataset_manager.azure.client import AzureAPIClient


class ImportsAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/groups"

    def deploy_dataset(self, workspace_id: str, dataset_pbix: bytes) -> str:
        imports_id = self._import_pbix(
            workspace_id=workspace_id,
            display_name="Dataset",
            pbix=dataset_pbix,
        )
        return self._get_dataset_id(import_id=imports_id, workspace_id=workspace_id)

    def deploy_visual(
        self, workspace_id: str, display_name: str, dataset_id: str, visual_pbix: bytes
    ) -> str:
        visual_with_connection = _replace_connection_string(
            visual_pbix,
            dataset_id=dataset_id,
        )
        return self._import_pbix(
            workspace_id=workspace_id,
            display_name=display_name,
            pbix=visual_with_connection,
        )

    def _import_pbix(
        self,
        workspace_id: str,
        display_name: str,
        pbix: bytes,
    ) -> str:
        url = f"{self._base_url}/{workspace_id}/imports?datasetDisplayName={display_name}&nameConflict=CreateOrOverwrite"
        encoder = MultipartEncoder({"value": ("value", pbix)})
        response = self._api.post(
            url,
            data=encoder.to_string(),
            headers={"Content-Type": encoder.content_type},
        )
        return response.json()["id"]

    def _get_dataset_id(self, import_id: str, workspace_id: str) -> str:
        url = f"{self._base_url}/{workspace_id}/imports/{import_id}"
        response = self._api.get(url)
        while response.json()["importState"] != "Succeeded":
            sleep(5)
            response = self._api.get(url)
        return response.json()["datasets"][0]["id"]


def _replace_connection_string(visual_pbix: bytes, dataset_id: str) -> bytes:
    connection_string = {
        "Version": 3,
        "Connections": [
            {
                "Name": "EntityDataSource",
                "ConnectionString": f'Data Source=pbiazure://api.powerbi.com;Initial Catalog={dataset_id};Identity Provider="https://login.microsoftonline.com/common, https://analysis.windows.net/powerbi/api, 7f67af8a-fedc-4b08-8b4e-37c4d127b6cf";Integrated Security=ClaimsToken',
                "ConnectionType": "pbiServiceLive",
                "PbiServiceModelId": 0,
                "PbiModelVirtualServerName": "sobe_wowvirtualserver",
                "PbiModelDatabaseName": dataset_id,
            }
        ],
    }
    visual_with_connection = io.BytesIO()
    with (
        ZipFile(io.BytesIO(visual_pbix)) as original,
        ZipFile(visual_with_connection, "w", compression=ZIP_DEFLATED) as modified,
    ):
        for original_file in original.infolist():
            if original_file.filename == "Connections":
                modified.writestr(original_file, json.dumps(connection_string))
            else:
                with original.open(original_file.filename) as f:
                    modified.writestr(original_file, f.read())
    return visual_with_connection.getvalue()
