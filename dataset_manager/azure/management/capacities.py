from dataset_manager.azure.client import AzureAPIClient
from dataset_manager.entities import CapacityName


class ManagementCapacitiesAPI:
    def __init__(
        self,
        subscription_id: str,
        resource_group_name: str,
        azure_api_client: AzureAPIClient,
    ) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = f"https://management.azure.com/subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/providers/Microsoft.PowerBIDedicated/capacities"

    def get_all(self) -> dict:
        url = f"{self._base_url}?api-version=2021-01-01"
        return self._api.get(url).json()

    def details(self, capacity_name: CapacityName) -> dict:
        url = f"{self._base_url}/{capacity_name}?api-version=2021-01-01"
        return self._api.get(url).json()

    def resume(self, capacity_name: CapacityName) -> None:
        url = f"{self._base_url}/{capacity_name}/resume?api-version=2021-01-01"
        self._api.post(url)

    def pause(self, capacity_name: CapacityName) -> None:
        url = f"{self._base_url}/{capacity_name}/suspend?api-version=2021-01-01"
        self._api.post(url)

    def scale(self, capacity_name: CapacityName, tier: str) -> None:
        url = f"{self._base_url}/{capacity_name}?api-version=2021-01-01"
        self._api.patch(url, json={"sku": {"name": tier, "tier": "PBIE_Azure"}})


def get_management_capacities_api(
    config, auth_token_provider
) -> ManagementCapacitiesAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=auth_token_provider)
    return ManagementCapacitiesAPI(
        subscription_id=config.AZURE_SUBSCRIPTION_ID,
        resource_group_name=config.AZURE_RESOURCE_GROUP_NAME,
        azure_api_client=azure_api_client,
    )
