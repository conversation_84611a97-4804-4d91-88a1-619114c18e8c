import asyncio
import functools
import logging
import signal
from collections.abc import Callable
from datetime import UTC, datetime, timedelta
from typing import ParamSpec, Protocol, TypeVar

import anyio.to_thread
import kubernetes.client
import kubernetes.config
from kubernetes.client.exceptions import ApiException

log = logging.getLogger(__name__)

P = ParamSpec("P")
T = TypeVar("T")


async def _run_in_threadpool(
    request_func: Callable[P, T], *args: P.args, **kwargs: P.kwargs
) -> T:
    func = functools.partial(request_func, *args, **kwargs)
    return await anyio.to_thread.run_sync(func)


class LeaderElectionFailed(Exception): ...


class LeaderElection:
    def __init__(  # noqa: PLR0913
        self,
        coordination_api: kubernetes.client.CoordinationV1Api,
        name: str,
        namespace: str,
        identity: str,
        lease_duration_seconds: int = 15,
        renew_deadline: int = 10,
        renew_period: int = 2,
    ):
        self._coordination_api = coordination_api
        self._name = name
        self._namespace = namespace
        self._identity = identity
        self._lease_duration_seconds = lease_duration_seconds
        self._renew_deadline = renew_deadline
        self._renew_period = renew_period

        self.is_leader = False
        self._stop = asyncio.Event()
        self._task: asyncio.Task | None = None
        self._max_backoff = 30

    async def _get_lease(self) -> kubernetes.client.V1Lease | None:
        try:
            return await _run_in_threadpool(
                self._coordination_api.read_namespaced_lease,
                self._name,
                self._namespace,
            )
        except ApiException as api_error:
            if api_error.status == 404:  # noqa: PLR2004
                return None
            raise

    async def _create_lease(self) -> kubernetes.client.V1Lease:
        now = datetime.now(UTC)
        lease: kubernetes.client.V1Lease | None = kubernetes.client.V1Lease(
            metadata=kubernetes.client.V1ObjectMeta(
                name=self._name, namespace=self._namespace
            ),
            spec=kubernetes.client.V1LeaseSpec(
                holder_identity=self._identity,
                lease_duration_seconds=self._lease_duration_seconds,
                acquire_time=now,
                renew_time=now,
                lease_transitions=0,
            ),
        )
        try:
            return await _run_in_threadpool(
                self._coordination_api.create_namespaced_lease, self._namespace, lease
            )
        except ApiException as api_error:
            if api_error.status == 409:  # noqa: PLR2004
                if lease := await self._get_lease():
                    return lease
                raise LeaderElectionFailed("Lease doesn't exist, but it should")
            raise

    def _is_lease_expired(self, lease: kubernetes.client.V1Lease) -> bool:
        now = datetime.now(UTC)
        spec = lease.spec or kubernetes.client.V1LeaseSpec()
        lease_time = spec.renew_time or spec.acquire_time
        return (
            lease_time is None
            or spec.lease_duration_seconds is None
            or now >= lease_time + timedelta(seconds=spec.lease_duration_seconds)
        )

    async def _renew(
        self, lease: kubernetes.client.V1Lease
    ) -> kubernetes.client.V1Lease:
        now = datetime.now(UTC)
        spec = lease.spec or kubernetes.client.V1LeaseSpec()
        new_lease = kubernetes.client.V1Lease(
            metadata=kubernetes.client.V1ObjectMeta(
                name=self._name,
                namespace=self._namespace,
                resource_version=lease.metadata.resource_version,
            ),
            spec=kubernetes.client.V1LeaseSpec(
                holder_identity=self._identity,
                lease_duration_seconds=self._lease_duration_seconds,
                acquire_time=spec.acquire_time or now,
                renew_time=now,
                lease_transitions=spec.lease_transitions or 0,
            ),
        )
        return await _run_in_threadpool(
            self._coordination_api.replace_namespaced_lease,
            self._name,
            self._namespace,
            new_lease,
        )

    async def _renew_within_deadline(self, lease: kubernetes.client.V1Lease) -> bool:
        start = datetime.now(UTC)
        while (datetime.now(UTC) - start).total_seconds() < self._renew_deadline:
            try:
                lease = await self._renew(lease)
                return True
            except ApiException as api_error:
                # Kubernetes API returns 409 on resource change, this usually means that
                # there is new leader, but is not guaranteed, so we try to renew anyway
                if api_error.status == 409:  # noqa: PLR2004
                    updated_lease = await self._get_lease()
                    if updated_lease is None:
                        raise LeaderElectionFailed("Lease doesn't exist, but it should")
                    lease = updated_lease
                    continue
                else:
                    raise
        return False

    async def _takeover(
        self, lease: kubernetes.client.V1Lease
    ) -> kubernetes.client.V1Lease | None:
        now = datetime.now(UTC)
        transitions = (lease.spec.lease_transitions or 0) + 1
        new_lease = kubernetes.client.V1Lease(
            metadata=kubernetes.client.V1ObjectMeta(
                name=self._name,
                namespace=self._namespace,
                resource_version=lease.metadata.resource_version,
            ),
            spec=kubernetes.client.V1LeaseSpec(
                holder_identity=self._identity,
                lease_duration_seconds=self._lease_duration_seconds,
                acquire_time=now,
                renew_time=now,
                lease_transitions=transitions,
            ),
        )
        try:
            return await _run_in_threadpool(
                self._coordination_api.replace_namespaced_lease,
                self._name,
                self._namespace,
                new_lease,
            )
        except ApiException as api_error:
            if api_error.status == 409:  # noqa: PLR2004
                return None
            raise

    def _is_holder(self, lease: kubernetes.client.V1Lease) -> bool:
        return lease.spec is not None and lease.spec.holder_identity == self._identity

    async def _run(self):
        backoff = self._renew_period
        while not self._stop.is_set():
            try:
                lease = await self._get_lease()

                # there is no leader yet
                if lease is None:
                    # try to become a leader
                    lease = await self._create_lease()
                    if lease and self._is_holder(lease):
                        # managed to become a leader
                        self.is_leader = True
                        await asyncio.sleep(self._renew_period)
                        continue

                if self._is_holder(lease):
                    # leader, renew the lease
                    self.is_leader = await self._renew_within_deadline(lease)
                elif self._is_lease_expired(lease):
                    # not the leader, but lease expired, try to takeover
                    lease = await self._takeover(lease)
                    self.is_leader = lease is not None and self._is_holder(lease)
                else:
                    # not a leader
                    self.is_leader = False

                await asyncio.sleep(self._renew_period)
                backoff = self._renew_period
            except Exception:
                self.is_leader = False
                log.exception("Error in leader election, backing off")
                await asyncio.sleep(min(self._max_backoff, backoff))
                backoff = min(self._max_backoff, backoff * 2)

    def start(self):
        if self._task is None:
            self._stop.clear()
            self._task = asyncio.create_task(self._run(), name="leader-election")

    async def stop(self):
        self._stop.set()
        if self._task:
            try:
                await self._task
            finally:
                self._task = None


class LeaderElectionSettings(Protocol):
    service_name: str
    pod_namespace: str
    pod_name: str
    lease_duration_seconds: int
    lease_renew_deadline_seconds: int
    lease_renew_period_seconds: int


def start_leader_election(settings: LeaderElectionSettings) -> LeaderElection:
    kubernetes.config.load_incluster_config()
    kubernetes_api = kubernetes.client.ApiClient()
    coordination_api = kubernetes.client.CoordinationV1Api(kubernetes_api)
    leader_election = LeaderElection(
        coordination_api,
        settings.service_name,
        settings.pod_namespace,
        identity=settings.pod_name,
        lease_duration_seconds=settings.lease_duration_seconds,
        renew_deadline=settings.lease_renew_deadline_seconds,
        renew_period=settings.lease_renew_period_seconds,
    )

    leader_election.start()
    return leader_election


async def run_forever_as_leader(
    leader_election: LeaderElection,
    func: Callable,
    sleep_between_runs_seconds: int = 10,
    delay_on_start_seconds: int = 60,
    *func_args,
    **func_kwargs,
):
    await asyncio.sleep(delay_on_start_seconds)
    stop_event = asyncio.Event()

    def termination_handler(_signum, _frame):
        stop_event.set()

    signal.signal(signal.SIGTERM, termination_handler)
    signal.signal(signal.SIGINT, termination_handler)

    while not stop_event.is_set():
        if leader_election.is_leader:
            try:
                await _run_in_threadpool(func, *func_args, **func_kwargs)
            except Exception:
                log.exception("Error when executing '%s'", func.__name__)
        await asyncio.sleep(sleep_between_runs_seconds)
