import asyncio

import sentry_sdk


def before_send_transaction(event, hint):
    # ignore errors logged by elasticapm logger
    if "elasticapm" in event.get("logger", ""):
        return None

    exception_values = event.get("exception", {}).get("values", [{}])[0]
    # ignore all exceptions defined in elasticapm package
    if "elasticapm" in exception_values.get("module", ""):
        return None

    # ignore ClientDisconnected exception triggered inside Elastic APM wrapper
    if "uvicorn" in exception_values.get(
        "module", ""
    ) and "ClientDisconnected" in exception_values.get("type", ""):
        return None

    # ignore health checks
    if event.get("type") == "transaction" and "/health" in event.get("transaction"):
        return None

    return event


sentry_sdk.init(
    traces_sample_rate=1.0,
    before_send_transaction=before_send_transaction,
)

import logging
from contextlib import asynccontextmanager

from elasticapm.contrib.starlette import ElasticAPM, make_apm_client
from fastapi import Depends, FastAPI, Request
from fastapi.openapi.utils import get_openapi

from dataset_manager.api import (
    capacity,
    dataset,
    debug,
    demo_mode,
    exceptions,
    healthcheck,
    profile,
    shard,
    studio,
    user,
    version,
)
from dataset_manager.api.api_key import require_key
from dataset_manager.config import (
    APP_VERSION,
    ELASTIC_SECRET_TOKEN,
    ELASTIC_SERVICE_URL,
    ENV,
    HEALTHCHECK_URL,
    SERVICE_NAME,
    get_modern_settings,
)
from dataset_manager.leader_election import run_forever_as_leader, start_leader_election
from dataset_manager.logs import configure_logger
from dataset_manager.periodic_ops.capacities import check_if_all_capacities_are_unpaused
from dataset_manager.periodic_ops.releases import (
    assign_requested_releases,
    handle_requested_assign_concurrently,
    handle_requested_shard_concurrently,
)

configure_logger()
log = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    settings = get_modern_settings()
    leader_election = start_leader_election(settings)
    assign_requested_releases_task = asyncio.create_task(
        run_forever_as_leader(
            leader_election,
            assign_requested_releases,
            sleep_between_runs_seconds=10,
            delay_on_start_seconds=40,
        )
    )

    handle_requested_assign_concurrently_task = asyncio.create_task(
        handle_requested_assign_concurrently(leader_election)
    )
    handle_requested_shard_concurrently_task = asyncio.create_task(
        handle_requested_shard_concurrently(leader_election)
    )
    check_if_all_capacities_are_unpaused_task = asyncio.create_task(
        run_forever_as_leader(
            leader_election,
            check_if_all_capacities_are_unpaused,
            sleep_between_runs_seconds=300,
        )
    )

    yield

    assign_requested_releases_task.cancel()
    handle_requested_assign_concurrently_task.cancel()
    handle_requested_shard_concurrently_task.cancel()
    check_if_all_capacities_are_unpaused_task.cancel()

    await leader_election.stop()


app = FastAPI(
    title="Dataset Manager",
    version=APP_VERSION,
    dependencies=[Depends(require_key)],
    lifespan=lifespan,
)


app.include_router(studio.router)
app.include_router(shard.router)
app.include_router(version.router)
app.include_router(capacity.router)
app.include_router(healthcheck.router)
app.include_router(debug.router)
app.include_router(dataset.public_router, prefix="/dataset/by-user/{user_id}")
app.include_router(dataset.shared_router, prefix="/dataset/by-user/{user_id}")
app.include_router(dataset.private_router, prefix="/dataset/by-user/{user_id}")
app.include_router(demo_mode.router)
app.include_router(profile.router)
app.include_router(user.router)


@app.middleware("http")
async def handle_domain_exceptions(request: Request, call_next):
    return await exceptions.handle_domain_exceptions(request, call_next)


if ELASTIC_SECRET_TOKEN:
    # ElasticAPM middleware need to be added after HTTP Middleware
    # https://www.elastic.co/guide/en/apm/agent/python/current/starlette-support.html
    app.add_middleware(
        ElasticAPM,  # type: ignore[arg-type]
        client=make_apm_client(
            {
                "SERVICE_NAME": SERVICE_NAME,
                "SECRET_TOKEN": ELASTIC_SECRET_TOKEN,
                "SERVER_URL": ELASTIC_SERVICE_URL,
                "ENVIRONMENT": ENV,
                "TRANSACTION_IGNORE_URLS": [HEALTHCHECK_URL, f"{HEALTHCHECK_URL}/*"],
                "PROCESSORS": [
                    (
                        "dataset_manager.tracing.elastic_tracer"
                        ".remove_x_api_key_from_stacktrace_headers"
                    ),
                    "elasticapm.processors.sanitize_stacktrace_locals",
                    "elasticapm.processors.sanitize_http_request_cookies",
                    "elasticapm.processors.sanitize_http_headers",
                    "elasticapm.processors.sanitize_http_wsgi_env",
                    "elasticapm.processors.sanitize_http_request_body",
                ],
            }
        ),
    )


@app.get("/openapi.json", include_in_schema=False)
async def openapi():
    return get_openapi(title=app.title, version=app.version, routes=app.routes)
